# Generate Speech Fixtures for Yandex SpeechKit STT Plugin
# This PowerShell script uses Windows SAPI to create real speech audio files

Write-Host "Yandex SpeechKit STT Plugin - Speech Fixture Generator" -ForegroundColor Green
Write-Host "========================================================" -ForegroundColor Green
Write-Host ""

# Create fixtures directory
$fixturesDir = "tests\fixtures"
if (!(Test-Path $fixturesDir)) {
    New-Item -ItemType Directory -Path $fixturesDir -Force | Out-Null
}

Write-Host "Creating speech fixtures in: $((Get-Location).Path)\$fixturesDir" -ForegroundColor Yellow
Write-Host ""

# Load SAPI
Add-Type -AssemblyName System.Speech
$synth = New-Object System.Speech.Synthesis.SpeechSynthesizer

# List available voices
Write-Host "Available voices:" -ForegroundColor Cyan
$voices = $synth.GetInstalledVoices()
for ($i = 0; $i -lt $voices.Count; $i++) {
    $voice = $voices[$i].VoiceInfo
    Write-Host "  $i`: $($voice.Name) ($($voice.Culture))" -ForegroundColor Gray
}
Write-Host ""

# Russian text samples
$russianTexts = @(
    "Привет, как дела? Меня зовут Анна.",
    "Сегодня хорошая погода для прогулки в парке.",
    "Я изучаю русский язык уже два года."
)

# English text samples  
$englishTexts = @(
    "Hello, how are you? My name is John.",
    "Today is a beautiful day for a walk in the park.",
    "I have been learning English for two years."
)

# Mixed language samples
$mixedTexts = @(
    "Hello, меня зовут Анна. I work in технологической компании.",
    "This is English, а это русский язык in the same sentence."
)

function Generate-SpeechFile {
    param(
        [string]$text,
        [string]$filename,
        [string]$voiceName = $null
    )
    
    $outputPath = Join-Path $fixturesDir $filename
    
    try {
        # Set voice if specified
        if ($voiceName) {
            $targetVoice = $synth.GetInstalledVoices() | Where-Object { $_.VoiceInfo.Name -like "*$voiceName*" } | Select-Object -First 1
            if ($targetVoice) {
                $synth.SelectVoice($targetVoice.VoiceInfo.Name)
                Write-Host "  Using voice: $($targetVoice.VoiceInfo.Name)" -ForegroundColor Gray
            }
        }
        
        # Set output to WAV file
        $synth.SetOutputToWaveFile($outputPath)
        
        # Speak the text
        $synth.Speak($text)
        
        # Reset output to default
        $synth.SetOutputToDefaultAudioDevice()
        
        # Check file was created
        if (Test-Path $outputPath) {
            $size = (Get-Item $outputPath).Length
            Write-Host "  Created: $filename ($size bytes)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  Failed: $filename (file not created)" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  Error creating $filename`: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Generate Russian samples
Write-Host "Generating Russian speech samples..." -ForegroundColor Cyan

# Try to find a Russian voice
$russianVoice = $synth.GetInstalledVoices() | Where-Object { 
    $_.VoiceInfo.Culture.Name -eq "ru-RU" -or $_.VoiceInfo.Name -like "*Russian*" 
} | Select-Object -First 1

if ($russianVoice) {
    Write-Host "Found Russian voice: $($russianVoice.VoiceInfo.Name)" -ForegroundColor Yellow
    $voiceForRussian = $russianVoice.VoiceInfo.Name
} else {
    Write-Host "No Russian voice found, using default voice" -ForegroundColor Yellow
    $voiceForRussian = $null
}

$successCount = 0
$totalCount = 0

# Generate Russian files
Generate-SpeechFile -text $russianTexts[0] -filename "russian_sample.wav" -voiceName $voiceForRussian
if ($?) { $successCount++ }
$totalCount++

Generate-SpeechFile -text $russianTexts[1] -filename "russian_long.wav" -voiceName $voiceForRussian  
if ($?) { $successCount++ }
$totalCount++

Generate-SpeechFile -text $russianTexts[2] -filename "russian_noisy.wav" -voiceName $voiceForRussian
if ($?) { $successCount++ }
$totalCount++

Write-Host ""

# Generate English samples
Write-Host "Generating English speech samples..." -ForegroundColor Cyan

# Try to find an English voice
$englishVoice = $synth.GetInstalledVoices() | Where-Object { 
    $_.VoiceInfo.Culture.Name -eq "en-US" -or $_.VoiceInfo.Name -like "*English*" 
} | Select-Object -First 1

if ($englishVoice) {
    Write-Host "Found English voice: $($englishVoice.VoiceInfo.Name)" -ForegroundColor Yellow
    $voiceForEnglish = $englishVoice.VoiceInfo.Name
} else {
    Write-Host "Using default voice for English" -ForegroundColor Yellow
    $voiceForEnglish = $null
}

Generate-SpeechFile -text $englishTexts[0] -filename "english_sample.wav" -voiceName $voiceForEnglish
if ($?) { $successCount++ }
$totalCount++

Generate-SpeechFile -text $englishTexts[1] -filename "english_long.wav" -voiceName $voiceForEnglish
if ($?) { $successCount++ }
$totalCount++

Generate-SpeechFile -text $englishTexts[2] -filename "english_noisy.wav" -voiceName $voiceForEnglish
if ($?) { $successCount++ }
$totalCount++

Write-Host ""

# Generate mixed language sample
Write-Host "Generating mixed language sample..." -ForegroundColor Cyan
$mixedText = $mixedTexts[0] + " " + $mixedTexts[1]
Generate-SpeechFile -text $mixedText -filename "mixed_ru_en.wav" -voiceName $voiceForEnglish
if ($?) { $successCount++ }
$totalCount++

Write-Host ""

# Generate silence file using FFmpeg if available
Write-Host "Generating silence file..." -ForegroundColor Cyan
try {
    $ffmpegPath = Get-Command ffmpeg -ErrorAction SilentlyContinue
    if ($ffmpegPath) {
        $silencePath = Join-Path $fixturesDir "silence.wav"
        & ffmpeg -y -f lavfi -i "anullsrc=r=16000:cl=mono" -t 5 -c:a pcm_s16le $silencePath 2>$null
        if (Test-Path $silencePath) {
            Write-Host "  Created: silence.wav" -ForegroundColor Green
            $successCount++
        }
        $totalCount++
    } else {
        Write-Host "  FFmpeg not found, skipping silence.wav" -ForegroundColor Yellow
    }
} catch {
    Write-Host "  Failed to create silence.wav" -ForegroundColor Red
}

# Clean up
$synth.Dispose()

Write-Host ""
Write-Host "Speech fixture generation complete!" -ForegroundColor Green
Write-Host "Success rate: $successCount/$totalCount files created" -ForegroundColor Yellow

if ($successCount -gt 0) {
    Write-Host ""
    Write-Host "Generated files:" -ForegroundColor Cyan
    Get-ChildItem "$fixturesDir\*.wav" | ForEach-Object {
        $size = [math]::Round($_.Length / 1KB, 1)
        Write-Host "  $($_.Name): $size KB" -ForegroundColor Gray
    }
    
    Write-Host ""
    Write-Host "✅ Real speech fixtures are now ready for testing!" -ForegroundColor Green
    Write-Host "Run functional tests with: make test_functional" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "❌ No speech fixtures were created successfully." -ForegroundColor Red
    Write-Host "Try installing additional TTS voices or use the basic fixture generator." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
