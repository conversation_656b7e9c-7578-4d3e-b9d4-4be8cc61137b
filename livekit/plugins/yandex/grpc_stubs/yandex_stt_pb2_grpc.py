# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Simplified Yandex SpeechKit STT gRPC service definitions.

This is a manual implementation based on the proto files until we can properly generate
them.
"""

from typing import AsyncIterator, Iterator

import grpc

from .yandex_stt_pb2 import StreamingRequest, StreamingResponse


class RecognizerStub:
    """GRPC stub for Yandex SpeechKit Recognizer service."""

    def __init__(self, channel: grpc.Channel):
        """Initialize the stub with a gRPC channel."""
        self.channel = channel
        self._method_RecognizeStreaming = channel.stream_stream(
            "/speechkit.stt.v3.Recognizer/RecognizeStreaming",
            request_serializer=self._serialize_streaming_request,
            response_deserializer=self._deserialize_streaming_response,
        )

    def RecognizeStreaming(
        self,
        request_iterator: Iterator[StreamingRequest],
        timeout: float | None = None,
        metadata: list[tuple[str, str]] | None = None,
        credentials: grpc.CallCredentials | None = None,
        wait_for_ready: bool | None = None,
        compression: grpc.Compression | None = None,
    ) -> Iterator[StreamingResponse]:
        """Bidirectional streaming RPC for speech recognition.

        Args:
            request_iterator: Iterator of StreamingRequest messages
            timeout: Request timeout in seconds
            metadata: gRPC metadata
            credentials: Call credentials
            wait_for_ready: Whether to wait for the channel to be ready
            compression: Compression algorithm

        Returns:
            Iterator of StreamingResponse messages
        """
        return self._method_RecognizeStreaming(
            request_iterator,
            timeout=timeout,
            metadata=metadata,
            credentials=credentials,
            wait_for_ready=wait_for_ready,
            compression=compression,
        )

    @staticmethod
    def _serialize_streaming_request(request: StreamingRequest) -> bytes:
        """Serialize StreamingRequest to bytes."""
        # This is a simplified serialization - in real implementation
        # this would use protobuf serialization
        import json
        import pickle

        try:
            # Convert dataclass to dict for JSON serialization
            data = {
                "session_options": (
                    request.session_options.__dict__ if request.session_options else None
                ),
                "chunk": {"data": request.chunk.data.hex()} if request.chunk else None,
                "silence_chunk": request.silence_chunk.__dict__ if request.silence_chunk else None,
                "eou": {} if request.eou else None,
            }
            return json.dumps(data).encode("utf-8")
        except Exception:
            # Fallback to pickle for complex objects
            return pickle.dumps(request)

    @staticmethod
    def _deserialize_streaming_response(data: bytes) -> StreamingResponse:
        """Deserialize bytes to StreamingResponse."""
        # This is a simplified deserialization - in real implementation
        # this would use protobuf deserialization
        import json
        import pickle

        try:
            # Try JSON first
            data_dict = json.loads(data.decode("utf-8"))
            # Convert back to StreamingResponse object
            # This is simplified - real implementation would properly reconstruct the object
            return StreamingResponse()
        except Exception:
            # Fallback to pickle
            try:
                return pickle.loads(data)
            except Exception:
                # Return empty response if deserialization fails
                return StreamingResponse()


class AsyncRecognizerStub:
    """Async gRPC stub for Yandex SpeechKit Recognizer service."""

    def __init__(self, channel: grpc.aio.Channel):
        """Initialize the async stub with a gRPC channel."""
        self.channel = channel
        self._method_RecognizeStreaming = channel.stream_stream(
            "/speechkit.stt.v3.Recognizer/RecognizeStreaming",
            request_serializer=RecognizerStub._serialize_streaming_request,
            response_deserializer=RecognizerStub._deserialize_streaming_response,
        )

    async def RecognizeStreaming(
        self,
        request_iterator: AsyncIterator[StreamingRequest],
        timeout: float | None = None,
        metadata: list[tuple[str, str]] | None = None,
        credentials: grpc.CallCredentials | None = None,
        wait_for_ready: bool | None = None,
        compression: grpc.Compression | None = None,
    ) -> AsyncIterator[StreamingResponse]:
        """Async bidirectional streaming RPC for speech recognition.

        Args:
            request_iterator: Async iterator of StreamingRequest messages
            timeout: Request timeout in seconds
            metadata: gRPC metadata
            credentials: Call credentials
            wait_for_ready: Whether to wait for the channel to be ready
            compression: Compression algorithm

        Returns:
            Async iterator of StreamingResponse messages
        """
        call = self._method_RecognizeStreaming(
            request_iterator,
            timeout=timeout,
            metadata=metadata,
            credentials=credentials,
            wait_for_ready=wait_for_ready,
            compression=compression,
        )

        async for response in call:
            yield response


# Service registration functions
def add_RecognizerServicer_to_server(servicer, server):
    """Add Recognizer servicer to gRPC server."""
    # This would be implemented for server-side usage
    pass


def create_recognizer_stub(channel: grpc.Channel) -> RecognizerStub:
    """Create a Recognizer stub from a gRPC channel."""
    return RecognizerStub(channel)


def create_async_recognizer_stub(channel: grpc.aio.Channel) -> AsyncRecognizerStub:
    """Create an async Recognizer stub from a gRPC channel."""
    return AsyncRecognizerStub(channel)
