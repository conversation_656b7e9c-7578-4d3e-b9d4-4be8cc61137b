# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Simplified Yandex SpeechKit STT gRPC message definitions.
This is a manual implementation based on the proto files until we can properly generate them.
"""

from dataclasses import dataclass
from typing import List, Optional, Union
from enum import Enum


class AudioEncoding(Enum):
    """Audio encoding types supported by Yandex SpeechKit."""
    AUDIO_ENCODING_UNSPECIFIED = 0
    LINEAR16_PCM = 1


class TextNormalization(Enum):
    """Text normalization options."""
    TEXT_NORMALIZATION_UNSPECIFIED = 0
    TEXT_NORMALIZATION_ENABLED = 1
    TEXT_NORMALIZATION_DISABLED = 2


@dataclass
class TextNormalizationOptions:
    """Text normalization configuration."""
    text_normalization: TextNormalization = TextNormalization.TEXT_NORMALIZATION_ENABLED
    profanity_filter: bool = False
    literature_text: bool = False


@dataclass
class RawAudio:
    """Raw audio format specification."""
    audio_encoding: AudioEncoding = AudioEncoding.LINEAR16_PCM
    sample_rate_hertz: int = 16000
    audio_channel_count: int = 1


@dataclass
class AudioFormatOptions:
    """Audio format options."""
    raw_audio: Optional[RawAudio] = None


@dataclass
class RecognitionModelOptions:
    """Recognition model configuration."""
    model: str = "general"
    audio_format: Optional[AudioFormatOptions] = None
    text_normalization: Optional[TextNormalizationOptions] = None
    language_restriction: Optional[str] = None


@dataclass
class StreamingOptions:
    """Streaming recognition configuration."""
    recognition_model: Optional[RecognitionModelOptions] = None


@dataclass
class AudioChunk:
    """Audio data chunk."""
    data: bytes


@dataclass
class SilenceChunk:
    """Silence chunk."""
    duration_ms: int


@dataclass
class Eou:
    """End of utterance marker."""
    pass


@dataclass
class StreamingRequest:
    """Streaming recognition request."""
    session_options: Optional[StreamingOptions] = None
    chunk: Optional[AudioChunk] = None
    silence_chunk: Optional[SilenceChunk] = None
    eou: Optional[Eou] = None


@dataclass
class Word:
    """Recognized word with timing."""
    text: str
    start_time_ms: int
    end_time_ms: int


@dataclass
class LanguageEstimation:
    """Language detection result."""
    language_code: str
    probability: float


@dataclass
class Alternative:
    """Recognition alternative."""
    words: List[Word]
    text: str
    start_time_ms: int
    end_time_ms: int
    confidence: float
    languages: List[LanguageEstimation]


@dataclass
class AlternativeUpdate:
    """Recognition update."""
    alternatives: List[Alternative]
    channel_tag: str = ""


@dataclass
class EouUpdate:
    """End of utterance update."""
    time_ms: int


@dataclass
class AudioCursors:
    """Audio processing state."""
    received_data_ms: int = 0
    reset_time_ms: int = 0
    partial_time_ms: int = 0
    final_time_ms: int = 0
    final_index: int = 0
    eou_time_ms: int = 0


@dataclass
class SessionUuid:
    """Session identifier."""
    uuid: str
    user_request_id: str = ""


class CodeType(Enum):
    """Status code types."""
    CODE_TYPE_UNSPECIFIED = 0
    WORKING = 1
    WARNING = 2
    CLOSED = 3


@dataclass
class StatusCode:
    """Status message."""
    code_type: CodeType
    message: str


@dataclass
class StreamingResponse:
    """Streaming recognition response."""
    session_uuid: Optional[SessionUuid] = None
    audio_cursors: Optional[AudioCursors] = None
    response_wall_time_ms: int = 0
    partial: Optional[AlternativeUpdate] = None
    final: Optional[AlternativeUpdate] = None
    eou_update: Optional[EouUpdate] = None
    status_code: Optional[StatusCode] = None
    channel_tag: str = ""


# Helper functions for creating messages
def create_streaming_options(
    model: str = "general",
    language: str = "ru-RU",
    sample_rate: int = 16000,
    profanity_filter: bool = False,
) -> StreamingOptions:
    """Create streaming options with common parameters."""
    return StreamingOptions(
        recognition_model=RecognitionModelOptions(
            model=model,
            audio_format=AudioFormatOptions(
                raw_audio=RawAudio(
                    audio_encoding=AudioEncoding.LINEAR16_PCM,
                    sample_rate_hertz=sample_rate,
                    audio_channel_count=1,
                )
            ),
            text_normalization=TextNormalizationOptions(
                text_normalization=TextNormalization.TEXT_NORMALIZATION_ENABLED,
                profanity_filter=profanity_filter,
            ),
            language_restriction=language,
        )
    )


def create_audio_chunk(audio_data: bytes) -> StreamingRequest:
    """Create a streaming request with audio data."""
    return StreamingRequest(chunk=AudioChunk(data=audio_data))


def create_session_request(options: StreamingOptions) -> StreamingRequest:
    """Create a streaming request with session options."""
    return StreamingRequest(session_options=options)
