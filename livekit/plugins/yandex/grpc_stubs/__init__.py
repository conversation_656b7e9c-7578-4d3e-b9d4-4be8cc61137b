# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Yandex SpeechKit gRPC stubs package.

This package contains simplified gRPC message and service definitions for the Yandex
SpeechKit STT API.
"""

from .yandex_stt_pb2 import (
    Alternative,
    AlternativeUpdate,
    AudioChunk,
    AudioCursors,
    CodeType,
    EouUpdate,
    SessionUuid,
    StatusCode,
    StreamingOptions,
    StreamingRequest,
    StreamingResponse,
    create_audio_chunk,
    create_session_request,
    create_streaming_options,
)
from .yandex_stt_pb2_grpc import (
    AsyncRecognizerStub,
    RecognizerStub,
    create_async_recognizer_stub,
    create_recognizer_stub,
)

__all__ = [
    # Message types
    "StreamingRequest",
    "StreamingResponse",
    "StreamingOptions",
    "AudioChunk",
    "Alternative",
    "AlternativeUpdate",
    "EouUpdate",
    "AudioCursors",
    "SessionUuid",
    "StatusCode",
    "CodeType",
    # Helper functions
    "create_streaming_options",
    "create_audio_chunk",
    "create_session_request",
    # Service stubs
    "RecognizerStub",
    "AsyncRecognizerStub",
    "create_recognizer_stub",
    "create_async_recognizer_stub",
]
