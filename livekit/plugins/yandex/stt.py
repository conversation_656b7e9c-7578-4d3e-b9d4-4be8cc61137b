# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import annotations

import asyncio
import json
import os
import time
import weakref
from dataclasses import dataclass
from typing import Any, AsyncIterator

import aiohttp
import grpc
import numpy as np
from livekit.agents import (
    DEFAULT_API_CONNECT_OPTIONS,
    APIConnectionError,
    APIConnectOptions,
    APIStatusError,
    APITimeoutError,
    stt,
    utils,
)
from livekit.agents.types import NOT_GIVEN, NotGivenOr
from livekit.agents.utils import AudioBuffer, is_given

from livekit import rtc

from ._utils import YandexCredentials, convert_audio_frame_to_pcm, create_grpc_metadata
from .grpc_stubs import (
    StreamingRequest,
    StreamingResponse,
    create_async_recognizer_stub,
    create_audio_chunk,
    create_session_request,
    create_streaming_options,
)
from .log import logger
from .models import YandexAudioEncoding, YandexSTTLanguages, YandexSTTModels


@dataclass
class STTOptions:
    """Configuration options for Yandex SpeechKit STT."""

    model: str
    language: str | None
    detect_language: bool
    interim_results: bool
    profanity_filter: bool
    sample_rate: int
    audio_encoding: str
    folder_id: str
    grpc_endpoint: str


class STT(stt.STT):
    """Yandex SpeechKit Speech-to-Text implementation."""

    def __init__(
        self,
        *,
        model: YandexSTTModels | str = "general",
        language: YandexSTTLanguages | str = "ru-RU",
        detect_language: bool = False,
        interim_results: bool = True,
        profanity_filter: bool = False,
        sample_rate: int = 16000,
        api_key: str | None = None,
        folder_id: str | None = None,
        grpc_endpoint: str = "stt.api.cloud.yandex.net:443",
        http_session: aiohttp.ClientSession | None = None,
    ) -> None:
        """
        Create a new instance of Yandex SpeechKit STT.

        Args:
            model: Recognition model to use (default: "general")
            language: Language code for recognition (default: "ru-RU")
            detect_language: Enable automatic language detection (default: False)
            interim_results: Enable interim results (default: True)
            profanity_filter: Enable profanity filtering (default: False)
            sample_rate: Audio sample rate in Hz (default: 16000)
            api_key: Yandex Cloud API key
            folder_id: Yandex Cloud folder ID
            grpc_endpoint: gRPC endpoint for SpeechKit API
            http_session: Optional HTTP session for API requests

        Note:
            api_key and folder_id must be provided.
            These can also be set via environment variables:
            - YANDEX_API_KEY
            - YANDEX_FOLDER_ID
        """
        super().__init__(
            capabilities=stt.STTCapabilities(streaming=True, interim_results=interim_results)
        )

        # Get credentials from arguments or environment
        if api_key or folder_id:
            # Use provided arguments
            self._credentials = YandexCredentials(
                api_key=api_key,
                folder_id=folder_id,
            )
        else:
            # Load from environment
            self._credentials = YandexCredentials.from_env()

        if not self._credentials.folder_id:
            raise ValueError("Yandex Cloud folder_id is required")

        if not self._credentials.api_key:
            raise ValueError("Yandex Cloud API key is required")

        self._opts = STTOptions(
            model=model,
            language=language if not detect_language else None,
            detect_language=detect_language,
            interim_results=interim_results,
            profanity_filter=profanity_filter,
            sample_rate=sample_rate,
            audio_encoding="LINEAR16_PCM",
            folder_id=self._credentials.folder_id,
            grpc_endpoint=grpc_endpoint,
        )

        self._session = http_session
        self._streams = weakref.WeakSet[SpeechStream]()

    def _ensure_session(self) -> aiohttp.ClientSession:
        """Ensure HTTP session exists for token refresh operations."""
        if not self._session:
            self._session = utils.http_context.http_session()
        return self._session

    async def _recognize_impl(
        self,
        buffer: AudioBuffer,
        *,
        language: NotGivenOr[YandexSTTLanguages | str] = NOT_GIVEN,
        conn_options: APIConnectOptions = DEFAULT_API_CONNECT_OPTIONS,
    ) -> stt.SpeechEvent:
        """
        Recognize speech from audio buffer (batch recognition).

        Note: This implementation uses streaming recognition internally
        as Yandex SpeechKit v3 primarily focuses on streaming.
        """
        # For batch recognition, we'll use the streaming API internally
        # and collect all results
        config = self._sanitize_options(language=language)

        try:
            # Create a temporary stream for batch processing
            stream = SpeechStream(
                stt=self,
                opts=config,
                conn_options=conn_options,
                credentials=self._credentials,
            )

            # Send all audio data
            combined_frame = rtc.combine_audio_frames(buffer)
            await stream.push_frame(combined_frame)
            await stream.flush()

            # Wait for final result
            final_event = None
            async for event in stream:
                if event.type == stt.SpeechEventType.FINAL_TRANSCRIPT:
                    final_event = event
                    break

            await stream.aclose()

            if final_event:
                return final_event
            else:
                # Return empty result if no transcription
                return stt.SpeechEvent(
                    type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                    alternatives=[
                        stt.SpeechData(
                            text="",
                            language="",
                        )
                    ],
                )

        except Exception as e:
            if isinstance(e, (APIConnectionError, APIStatusError, APITimeoutError)):
                raise
            raise APIConnectionError() from e

    def stream(
        self,
        *,
        language: NotGivenOr[YandexSTTLanguages | str] = NOT_GIVEN,
        conn_options: APIConnectOptions = DEFAULT_API_CONNECT_OPTIONS,
    ) -> SpeechStream:
        """Create a streaming transcription session."""
        config = self._sanitize_options(language=language)
        stream = SpeechStream(
            stt=self,
            opts=config,
            conn_options=conn_options,
            credentials=self._credentials,
        )
        self._streams.add(stream)
        return stream

    def _sanitize_options(
        self, *, language: NotGivenOr[YandexSTTLanguages | str] = NOT_GIVEN
    ) -> STTOptions:
        """Sanitize and validate options."""
        config = STTOptions(
            model=self._opts.model,
            language=self._opts.language,
            detect_language=self._opts.detect_language,
            interim_results=self._opts.interim_results,
            profanity_filter=self._opts.profanity_filter,
            sample_rate=self._opts.sample_rate,
            audio_encoding=self._opts.audio_encoding,
            folder_id=self._opts.folder_id,
            grpc_endpoint=self._opts.grpc_endpoint,
        )

        if is_given(language):
            config.language = language
            config.detect_language = False

        if config.detect_language:
            config.language = None

        return config


class SpeechStream(stt.SpeechStream):
    """Yandex SpeechKit streaming speech recognition."""

    def __init__(
        self,
        *,
        stt: STT,
        opts: STTOptions,
        conn_options: APIConnectOptions,
        credentials: YandexCredentials,
    ) -> None:
        super().__init__(stt=stt, conn_options=conn_options, sample_rate=opts.sample_rate)

        self._opts = opts
        self._credentials = credentials
        self._grpc_channel: grpc.aio.Channel | None = None
        self._grpc_stub = None
        self._stream_call = None
        self._closed = False
        self._session_started = False

        logger.info("Initializing Yandex SpeechKit streaming session")

    async def _run(self) -> None:
        """Required abstract method implementation."""
        await self._main_task()

    async def _main_task(self) -> None:
        """Main streaming task with gRPC implementation."""
        try:
            logger.info("Starting Yandex SpeechKit streaming session")

            # Create gRPC channel
            self._grpc_channel = grpc.aio.secure_channel(
                self._opts.grpc_endpoint, grpc.ssl_channel_credentials()
            )

            # Create gRPC stub
            self._grpc_stub = create_async_recognizer_stub(self._grpc_channel)

            # Create metadata for authentication
            metadata = create_grpc_metadata(self._credentials)

            # Start streaming session
            await self._start_streaming_session(metadata)

        except Exception as e:
            logger.exception("Error in Yandex STT streaming task")
            if isinstance(e, grpc.RpcError):
                if e.code() == grpc.StatusCode.UNAUTHENTICATED:
                    raise APIConnectionError("Authentication failed") from e
                elif e.code() == grpc.StatusCode.UNAVAILABLE:
                    raise APIConnectionError("Service unavailable") from e
                else:
                    raise APIConnectionError(f"gRPC error: {e.details()}") from e
            raise

    async def _start_streaming_session(self, metadata: list[tuple[str, str]]) -> None:
        """Start the gRPC streaming session."""
        try:
            # Create streaming options
            streaming_opts = create_streaming_options(
                model=self._opts.model,
                language=self._opts.language,
                sample_rate=self._opts.sample_rate,
                profanity_filter=self._opts.profanity_filter,
            )

            # Create request iterator
            request_iterator = self._create_request_iterator(streaming_opts)

            # Start streaming call
            self._stream_call = self._grpc_stub.RecognizeStreaming(
                request_iterator,
                metadata=metadata,
                timeout=self._conn_options.timeout,
            )

            # Process responses
            async for response in self._stream_call:
                await self._process_response(response)

        except Exception as e:
            logger.exception("Error in streaming session")
            raise

    async def _create_request_iterator(self, streaming_opts):
        """Create async iterator for streaming requests."""
        # Send session options first
        yield create_session_request(streaming_opts)
        self._session_started = True

        # Process audio frames from the input queue
        while not self._closed:
            try:
                # Wait for audio frame with timeout
                frame = await asyncio.wait_for(self._input_ch.recv(), timeout=0.1)

                if frame is None:
                    break

                # Convert frame to audio chunk
                audio_data = convert_audio_frame_to_pcm(frame)
                yield create_audio_chunk(audio_data)

            except asyncio.TimeoutError:
                # No frame available, continue
                continue
            except Exception as e:
                logger.exception("Error processing audio frame")
                break

    async def _process_response(self, response: StreamingResponse) -> None:
        """Process streaming response from Yandex SpeechKit."""
        try:
            if response.partial:
                # Interim results
                for alternative in response.partial.alternatives:
                    event = stt.SpeechEvent(
                        type=stt.SpeechEventType.INTERIM_TRANSCRIPT,
                        alternatives=[
                            stt.SpeechData(
                                text=alternative.text,
                                language=self._opts.language or "ru-RU",
                                start_time=alternative.start_time_ms / 1000.0,
                                end_time=alternative.end_time_ms / 1000.0,
                                confidence=alternative.confidence,
                            )
                        ],
                    )
                    self._event_ch.send_nowait(event)

            if response.final:
                # Final results
                for alternative in response.final.alternatives:
                    event = stt.SpeechEvent(
                        type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                        alternatives=[
                            stt.SpeechData(
                                text=alternative.text,
                                language=self._opts.language or "ru-RU",
                                start_time=alternative.start_time_ms / 1000.0,
                                end_time=alternative.end_time_ms / 1000.0,
                                confidence=alternative.confidence,
                            )
                        ],
                    )
                    self._event_ch.send_nowait(event)

            if response.eou_update:
                # End of utterance
                event = stt.SpeechEvent(
                    type=stt.SpeechEventType.END_OF_SPEECH,
                    alternatives=[],
                )
                self._event_ch.send_nowait(event)

        except Exception as e:
            logger.exception("Error processing response")

    async def aclose(self) -> None:
        """Close the streaming session."""
        if self._closed:
            return

        self._closed = True
        logger.info("Closing Yandex SpeechKit streaming session")

        try:
            if self._stream_call:
                self._stream_call.cancel()

            if self._grpc_channel:
                await self._grpc_channel.close()
        except Exception as e:
            logger.warning(f"Error closing gRPC resources: {e}")

        await super().aclose()
