# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import asyncio
import os
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Callable

from dotenv import load_dotenv

from livekit import rtc


def load_env_file(env_path: str | Path | None = None) -> None:
    """Load environment variables from .env file.

    Args:
        env_path: Path to .env file. If None, looks for .env in current directory.
    """
    if env_path is None:
        env_path = Path.cwd() / ".env"
    else:
        env_path = Path(env_path)

    if not env_path.exists():
        return

    try:
        with open(env_path, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    key = key.strip()
                    value = value.strip().strip('"').strip("'")
                    if key and value:
                        os.environ[key] = value
    except Exception:
        # Silently ignore errors in .env file loading
        pass


@dataclass
class YandexCredentials:
    """Yandex Cloud authentication credentials."""

    api_key: str | None = None
    folder_id: str | None = None

    @classmethod
    def from_env(cls) -> "YandexCredentials":
        """Create credentials from environment variables.

        Returns:
            YandexCredentials instance
        """
        return cls(
            api_key=os.environ.get("YANDEX_API_KEY"),
            folder_id=os.environ.get("YANDEX_FOLDER_ID"),
        )


def convert_audio_frame_to_pcm(frame: rtc.AudioFrame) -> bytes:
    """Convert LiveKit AudioFrame to LINEAR16_PCM format for Yandex SpeechKit."""
    # Ensure the audio is in the correct format (16-bit PCM, little-endian)
    # Yandex expects LINEAR16_PCM format
    if frame.sample_rate != 16000:
        # TODO: Implement sample rate conversion if needed
        pass

    # Convert numpy array to bytes
    # The frame.data is already in the correct format for most cases
    return frame.data.tobytes()


def validate_language_code(language: str) -> str:
    """Validate and normalize language code for Yandex SpeechKit."""
    # Map common language codes to Yandex format
    language_mapping = {
        "ru": "ru-RU",
        "en": "en-US",
        "russian": "ru-RU",
        "english": "en-US",
    }

    normalized = language_mapping.get(language.lower(), language)
    return normalized


class PeriodicCollector:
    """Utility class for collecting periodic statistics."""

    def __init__(self, callback: Callable[[Any], None], duration: float):
        self._callback = callback
        self._duration = duration
        self._task: asyncio.Task | None = None
        self._data: list[Any] = []
        self._start_time = time.time()

    def start(self) -> None:
        """Start the periodic collection."""
        if self._task is None or self._task.done():
            self._task = asyncio.create_task(self._run())

    def stop(self) -> None:
        """Stop the periodic collection."""
        if self._task and not self._task.done():
            self._task.cancel()

    def add_data(self, data: Any) -> None:
        """Add data to the collection."""
        self._data.append(data)

    async def _run(self) -> None:
        """Run the periodic collection loop."""
        try:
            while True:
                await asyncio.sleep(self._duration)
                if self._data:
                    self._callback(self._data.copy())
                    self._data.clear()
        except asyncio.CancelledError:
            pass


def create_grpc_metadata(credentials: YandexCredentials) -> list[tuple[str, str]]:
    """Create gRPC metadata for authentication."""
    metadata = []

    if credentials.api_key:
        metadata.append(("authorization", f"Api-Key {credentials.api_key}"))

    if credentials.folder_id:
        metadata.append(("x-folder-id", credentials.folder_id))

    return metadata
