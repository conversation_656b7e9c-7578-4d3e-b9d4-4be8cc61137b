# Yandex SpeechKit STT Plugin - Development Commands

# Load environment variables from .env file if it exists
include .env
export

# Git information
GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
GIT_COMMIT := $(shell git rev-list -1 HEAD)
GIT_VERSION := $(shell git describe --tags --always)

.PHONY: help install lint lint_fix test test_basic test_functional test_unit test_integration clean build check_env
.DEFAULT_GOAL := help

# Default target
help:
	@echo "Available commands:"
	@echo "  install		  - Install development dependencies"
	@echo "  lint			 - Check code for style issues without fixing them"
	@echo "  lint_fix		 - Automatically fix code style issues"
	@echo "  test			 - Run all tests"
	@echo "  test_basic	   - Run basic functionality test"
	@echo "  test_unit		- Run unit tests only (no credentials required)"
	@echo "  test_integration - Run integration tests (requires .env with credentials)"
	@echo "  test_functional  - Run functional tests (requires .env and audio files)"
	@echo "  clean			- Clean build artifacts"
	@echo "  build			- Build the package"

# Install development dependencies
install:
	hatch env create
	hatch run pip install -e ".[dev]"

# Check code for style issues without fixing them
lint:
	@echo "Running linters..."
	hatch run docformatter --black --check --recursive livekit/ || echo ""
	hatch run black --check livekit/
	hatch run isort --check livekit/
	hatch run flake8 livekit/ --max-line-length=100 --extend-ignore=E203,W503
	hatch run pylint livekit/ --disable=missing-docstring,too-few-public-methods,too-many-arguments,too-many-instance-attributes,import-error
	hatch run mypy livekit/

# Automatically fix code style issues
lint_fix:
	@echo "Fixing code style..."
	hatch run docformatter --black --in-place --recursive livekit/ || echo ""
	hatch run black livekit/
	hatch run isort livekit/

# Environment check
check_env:
	ifndef YANDEX_API_KEY
		$(error YANDEX_API_KEY is not set)
	endif
	ifndef YANDEX_FOLDER_ID
		$(error YANDEX_FOLDER_ID is not set)
	endif

# Run all tests
test: check_env
	@echo "🧪 Running all tests..."
	hatch run pytest tests/ -v