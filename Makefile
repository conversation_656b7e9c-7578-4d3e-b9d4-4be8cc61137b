# Check code for style issues without fixing them
lint:
	docformatter --config pyproject.toml --black --check --recursive livekit/ || echo ""
	poetry run black --check livekit/
	poetry run isort --check livekit/
	poetry run flake8 livekit/
	poetry run pylint livekit/
	poetry run mypy livekit/

# Automatically fix code style issues
lint_fix:
	docformatter --config pyproject.toml --black --in-place --recursive livekit/ || echo ""
	poetry run black livekit/
	poetry run isort livekit/