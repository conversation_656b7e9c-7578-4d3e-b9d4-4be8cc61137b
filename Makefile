# Yandex SpeechKit STT Plugin - Development Commands

# Load environment variables from .env file if it exists
include .env
export

# Git information
GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
GIT_COMMIT := $(shell git rev-list -1 HEAD)
GIT_VERSION := $(shell git describe --tags --always)

.PHONY: help install lint lint_fix test test_basic test_functional test_unit test_integration clean build check_env
.DEFAULT_GOAL := tests

# Install development dependencies
install:
	hatch env create
	hatch run pip install -e ".[dev]"

# Check code for style issues without fixing them
lint:
	@echo "Running linters..."
	hatch run docformatter --black --check --recursive livekit/ || echo ""
	hatch run black --check livekit/
	hatch run isort --check livekit/
	hatch run flake8 livekit/
	hatch run pylint livekit/
	hatch run mypy livekit/

# Automatically fix code style issues
lint_fix:
	@echo "Fixing code style..."
	hatch run docformatter --black --in-place --recursive livekit/ || echo ""
	hatch run black livekit/
	hatch run isort livekit/

# Environment check
check_env:
	@echo "Checking required environment variables..."
	ifndef YANDEX_API_KEY
		$(warning YANDEX_API_KEY value: $(YANDEX_API_KEY))
		$(error YANDEX_API_KEY is not set)
	endif
	ifndef YANDEX_FOLDER_ID
		$(warning YANDEX_FOLDER_ID value: $(YANDEX_FOLDER_ID))
		$(error YANDEX_FOLDER_ID is not set) 
	endif
	@echo "Environment variables present"

# Legacy alias
tests: test

# Run all tests
test: check_env
	@echo "Running all tests..."
	hatch run pytest tests/ -v

# Run basic functionality test
test_basic:
	@echo "Running basic functionality test..."
	hatch run python test_basic.py

# Run unit tests only (no credentials required)
test_unit:
	@echo "Running unit tests..."
	hatch run pytest tests/unit/ -v --tb=short

# Run integration tests (requires .env with credentials)
test_integration: check_env
	@echo "Running integration tests..."
	hatch run pytest tests/integration/ -v --tb=short

# Run functional tests (requires .env and audio files)
test_functional: check_env
	@echo "Running functional tests..."
	hatch run pytest tests/functional/ -v --tb=short

# Generate test fixtures with real speech
fixtures:
	@echo "🎵 Generating speech test fixtures..."
	hatch run python generate_tts_fixtures.py

# Generate basic fixtures (tones and noise)
fixtures_basic:
	@echo "🎵 Generating basic test fixtures..."
	python generate_fixtures.py

# Build the package
build:
	@echo "📦 Building package..."
	hatch build

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

