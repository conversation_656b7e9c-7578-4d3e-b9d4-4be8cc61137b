# Yandex SpeechKit STT Plugin - Development Commands

# Load environment variables from .env file if it exists
include .env
export

# Git information
GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
GIT_COMMIT := $(shell git rev-list -1 HEAD)
GIT_VERSION := $(shell git describe --tags --always)

.PHONY: help install lint lint_fix test test_basic  test_unit test_integration clean build check_env
.DEFAULT_GOAL := tests

# Install development dependencies
install:
	hatch env create
	hatch run pip install -e ".[dev]"

# Check code for style issues without fixing them
lint:
	@echo "Running linters..."
	hatch run docformatter --black --check --recursive livekit/  tests/ || echo ""
	hatch run black --check livekit/ tests/
	hatch run isort --check livekit/ tests/
	hatch run flake8 livekit/ tests/
	hatch run pylint livekit/ tests/
	hatch run mypy livekit/ tests/

# Automatically fix code style issues
lint_fix:
	@echo "Fixing code style..."
	hatch run docformatter --black --in-place --recursive livekit/  tests/ || echo ""
	hatch run black livekit/ tests/
	hatch run isort livekit/ tests/

# Environment check
check_env:
	@echo "Checking required environment variables..."
	ifndef YANDEX_API_KEY
		$(warning YANDEX_API_KEY value: $(YANDEX_API_KEY))
		$(error YANDEX_API_KEY is not set)
	endif
	ifndef YANDEX_FOLDER_ID
		$(warning YANDEX_FOLDER_ID value: $(YANDEX_FOLDER_ID))
		$(error YANDEX_FOLDER_ID is not set) 
	endif
	@echo "Environment variables present"

# Run all tests
tests: check_env
	@echo "Running all tests..."
	hatch run pytest tests/ -v