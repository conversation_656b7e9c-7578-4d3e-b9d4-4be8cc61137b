# Yandex SpeechKit STT Plugin - Development Commands

.PHONY: help install lint lint_fix test test_basic clean build

# Default target
help:
	@echo "Available commands:"
	@echo "  install     - Install development dependencies"
	@echo "  lint        - Check code for style issues without fixing them"
	@echo "  lint_fix    - Automatically fix code style issues"
	@echo "  test        - Run all tests"
	@echo "  test_basic  - Run basic functionality test"
	@echo "  clean       - Clean build artifacts"
	@echo "  build       - Build the package"

# Install development dependencies
install:
	hatch env create
	hatch run pip install -e ".[dev]"

# Check code for style issues without fixing them
lint:
	@echo "Running linters..."
	hatch run docformatter --black --check --recursive livekit/ || echo ""
	hatch run black --check livekit/
	hatch run isort --check livekit/
	hatch run flake8 livekit/ --max-line-length=100 --extend-ignore=E203,W503
	hatch run pylint livekit/ --disable=missing-docstring,too-few-public-methods,too-many-arguments,too-many-instance-attributes,import-error
	hatch run mypy livekit/

# Automatically fix code style issues
lint_fix:
	@echo "Fixing code style..."
	hatch run docformatter --black --in-place --recursive livekit/ || echo ""
	hatch run black livekit/
	hatch run isort livekit/

# Run all tests
test:
	@echo "Running tests..."
	hatch run pytest tests/ -v

# Run basic functionality test
test_basic:
	@echo "Running basic functionality test..."
	hatch run python test_basic.py
