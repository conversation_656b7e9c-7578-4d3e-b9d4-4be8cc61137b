#!/usr/bin/env python3
"""
Basic functionality test for Yandex SpeechKit STT plugin.
"""

import sys
sys.path.insert(0, '.')

def test_basic_functionality():
    """Test basic plugin functionality."""
    try:
        print('🧪 Testing Yandex STT Plugin...')
        
        # Test basic imports
        from livekit.plugins.yandex import STT, YandexSTTLanguages
        print('✅ Plugin imports successful')
        
        # Test credentials
        from livekit.plugins.yandex._utils import YandexCredentials
        creds = YandexCredentials(api_key='test_key', folder_id='test_folder')
        print('✅ Credentials creation works')
        
        # Test STT class
        stt = STT(api_key='test_key', folder_id='test_folder', language='ru-RU')
        print('✅ STT class creation successful')
        print(f'   Language: {stt._opts.language}')
        print(f'   Model: {stt._opts.model}')
        print(f'   Sample rate: {stt._opts.sample_rate}')
        
        # Test gRPC stubs
        from livekit.plugins.yandex.grpc_stubs import create_streaming_options
        opts = create_streaming_options(language='ru-RU')
        print('✅ gRPC stubs work')
        print(f'   Recognition model: {opts.recognition_model.model}')
        
        # Test streaming creation (without actual connection)
        stream = stt.stream()
        print('✅ Stream creation works')
        
        print()
        print('🎉 All tests passed! Plugin is functional.')
        return True
        
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_basic_functionality()
    sys.exit(0 if success else 1)
