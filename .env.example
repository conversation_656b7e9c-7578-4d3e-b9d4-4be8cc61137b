# Yandex SpeechKit STT Plugin Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# REQUIRED CREDENTIALS
# =============================================================================

# Yandex Cloud API Key (Service Account Key)
# Get this from: https://cloud.yandex.com/en/docs/iam/operations/api-key/create
YANDEX_API_KEY=your_service_account_api_key_here

# Yandex Cloud Folder ID
# Find this in your Yandex Cloud console
YANDEX_FOLDER_ID=your_folder_id_here

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Yandex SpeechKit gRPC endpoint (default: stt.api.cloud.yandex.net:443)
# YANDEX_STT_ENDPOINT=stt.api.cloud.yandex.net:443

# Default language for speech recognition (default: ru-RU)
# YANDEX_STT_LANGUAGE=ru-RU

# Default recognition model (default: general)
# YANDEX_STT_MODEL=general

# Enable debug logging (default: false)
# YANDEX_STT_DEBUG=false

# =============================================================================
# LIVEKIT CONFIGURATION (if needed)
# =============================================================================

# LiveKit server URL (for testing)
# LIVEKIT_URL=wss://your-livekit-server.com

# LiveKit API key (for testing)
# LIVEKIT_API_KEY=your_livekit_api_key

# LiveKit API secret (for testing)
# LIVEKIT_API_SECRET=your_livekit_api_secret

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Log level for development
# LOG_LEVEL=DEBUG
