# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Pytest configuration for functional tests.

Functional tests require real Yandex Cloud credentials and may make actual API calls.
They test end-to-end functionality with the Yandex SpeechKit service.
"""

import os
import sys
from pathlib import Path

import pytest

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from livekit.plugins.yandex import STT
from livekit.plugins.yandex._utils import YandexCredentials


@pytest.fixture
def real_credentials() -> YandexCredentials:
    """
    Provide real credentials from environment for functional tests.
    
    Raises:
        pytest.skip: If real credentials are not available
    """
    api_key = os.environ.get("YANDEX_API_KEY")
    folder_id = os.environ.get("YANDEX_FOLDER_ID")
    
    if not folder_id:
        pytest.skip("YANDEX_FOLDER_ID not set - skipping functional test")
    
    if not api_key:
        pytest.skip("YANDEX_API_KEY not set - skipping functional test")
    
    return YandexCredentials(
        api_key=api_key,
        folder_id=folder_id,
    )


@pytest.fixture
def real_stt(real_credentials: YandexCredentials) -> STT:
    """Create STT instance with real credentials for functional tests."""
    return STT(
        api_key=real_credentials.api_key,
        folder_id=real_credentials.folder_id,
        language="ru-RU",
    )


@pytest.fixture
def fixtures_dir() -> Path:
    """Path to test fixtures directory."""
    return Path(__file__).parent.parent / "fixtures"


@pytest.fixture
def sample_audio_files(fixtures_dir: Path) -> dict[str, Path]:
    """
    Dictionary of available sample audio files.
    
    Returns:
        dict: Mapping of language codes to audio file paths
    """
    audio_files = {}
    
    # Check for Russian audio samples
    russian_files = [
        "russian_sample.wav",
        "russian_long.wav", 
        "russian_noisy.wav",
    ]
    
    for filename in russian_files:
        file_path = fixtures_dir / filename
        if file_path.exists():
            audio_files[f"ru_{filename.split('_')[1].split('.')[0]}"] = file_path
    
    # Check for English audio samples
    english_files = [
        "english_sample.wav",
        "english_long.wav",
        "english_noisy.wav", 
    ]
    
    for filename in english_files:
        file_path = fixtures_dir / filename
        if file_path.exists():
            audio_files[f"en_{filename.split('_')[1].split('.')[0]}"] = file_path
    
    # Check for mixed language samples
    mixed_files = [
        "mixed_ru_en.wav",
        "silence.wav",
    ]
    
    for filename in mixed_files:
        file_path = fixtures_dir / filename
        if file_path.exists():
            audio_files[filename.split('.')[0]] = file_path
    
    return audio_files


@pytest.fixture
def skip_if_no_audio():
    """Skip test if no audio files are available."""
    def _skip_if_no_audio(audio_files: dict):
        if not audio_files:
            pytest.skip("No audio test files available - see tests/fixtures/README.md")
    return _skip_if_no_audio


def validate_credentials() -> bool:
    """Validate that required credentials are available."""
    has_api_key = bool(os.environ.get("YANDEX_API_KEY"))
    has_folder_id = bool(os.environ.get("YANDEX_FOLDER_ID"))
    
    return has_folder_id and has_api_key


@pytest.fixture
def skip_if_no_credentials():
    """Skip test if no real credentials are available."""
    def _skip_if_no_credentials():
        if not validate_credentials():
            pytest.skip("Missing Yandex credentials - ensure YANDEX_FOLDER_ID and YANDEX_API_KEY are set")
    return _skip_if_no_credentials


# Pytest configuration for functional tests
def pytest_configure(config):
    """Configure functional test markers."""
    config.addinivalue_line(
        "markers", "functional: mark test as a functional test (requires credentials and audio)"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their names and requirements."""
    for item in items:
        # Mark all tests in this directory as functional
        item.add_marker(pytest.mark.functional)
        
        # Mark slow tests
        if "slow" in item.nodeid or "performance" in str(item.function):
            item.add_marker(pytest.mark.slow)


@pytest.fixture(scope="session")
def event_loop():
    """Create an event loop for async tests."""
    import asyncio
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


def pytest_sessionstart(session):
    """Validate environment at functional test session start."""
    print("\nStarting Yandex SpeechKit STT Functional Tests")
    
    # Check for credentials (without exposing them)
    has_api_key = bool(os.environ.get("YANDEX_API_KEY"))
    has_folder_id = bool(os.environ.get("YANDEX_FOLDER_ID"))

    if has_api_key:
        print("Yandex credentials available")
    else:
        print("No Yandex credentials found")
        print("Set YANDEX_API_KEY for functional tests")

    if has_folder_id:
        print("Yandex folder ID available")
    else:
        print("No Yandex folder ID found")
        print("Set YANDEX_FOLDER_ID for functional tests")
    
    # Check for test fixtures
    fixtures_dir = Path(__file__).parent.parent / "fixtures"
    audio_files = list(fixtures_dir.glob("*.wav"))
    if audio_files:
        print(f"Found {len(audio_files)} audio test files")
    else:
        print("No audio test files found in tests/fixtures/")
        print("Add audio files for functional tests (see tests/fixtures/README.md)")
    
    print()


def pytest_sessionfinish(session, exitstatus):
    """Clean up after functional test session."""
    print(f"\nFunctional test session finished with exit status: {exitstatus}")
