# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Functional tests for Yandex SpeechKit STT plugin.

These tests require real Yandex Cloud credentials and may make actual API calls.
They test the integration with the Yandex SpeechKit service.

To run these tests:
1. Set up .env file with YANDEX_API_KEY and YANDEX_FOLDER_ID
2. Run: make test_functional
"""

import os
import pytest
from pathlib import Path

from livekit.plugins.yandex import STT
from livekit.plugins.yandex._utils import YandexCredentials


@pytest.mark.functional
class TestYandexSTTCredentials:
    """Functional tests for credential validation with real environment."""

    def test_real_credentials_loading(self, real_credentials):
        """Test loading real credentials from environment."""
        assert real_credentials.api_key is not None
        assert real_credentials.folder_id is not None
        assert len(real_credentials.api_key) > 10  # Basic sanity check
        assert len(real_credentials.folder_id) > 10  # Basic sanity check

    def test_stt_with_real_credentials(self, real_stt):
        """Test STT initialization with real credentials."""
        assert real_stt._credentials.api_key is not None
        assert real_stt._credentials.folder_id is not None
        assert real_stt._opts.language == "ru-RU"

    def test_grpc_metadata_with_real_credentials(self, real_credentials):
        """Test gRPC metadata creation with real credentials."""
        from livekit.plugins.yandex._utils import create_grpc_metadata
        
        metadata = create_grpc_metadata(real_credentials)
        
        assert len(metadata) == 2
        auth_header = next((value for key, value in metadata if key == "authorization"), None)
        folder_header = next((value for key, value in metadata if key == "x-folder-id"), None)
        
        assert auth_header is not None
        assert auth_header.startswith("Api-Key ")
        assert folder_header is not None
        assert len(folder_header) > 10


@pytest.mark.functional
class TestYandexSTTConfiguration:
    """Functional tests for STT configuration with real credentials."""

    def test_stt_different_languages(self, real_credentials):
        """Test STT with different language configurations."""
        # Test Russian
        stt_ru = STT(
            api_key=real_credentials.api_key,
            folder_id=real_credentials.folder_id,
            language="ru-RU"
        )
        assert stt_ru._opts.language == "ru-RU"
        
        # Test English
        stt_en = STT(
            api_key=real_credentials.api_key,
            folder_id=real_credentials.folder_id,
            language="en-US"
        )
        assert stt_en._opts.language == "en-US"

    def test_stt_different_models(self, real_credentials):
        """Test STT with different model configurations."""
        models_to_test = ["general"]  # Add more models as they become available
        
        for model in models_to_test:
            stt = STT(
                api_key=real_credentials.api_key,
                folder_id=real_credentials.folder_id,
                model=model
            )
            assert stt._opts.model == model

    def test_stt_language_detection(self, real_credentials):
        """Test STT with language detection enabled."""
        stt = STT(
            api_key=real_credentials.api_key,
            folder_id=real_credentials.folder_id,
            detect_language=True
        )
        
        config = stt._sanitize_options()
        assert config.detect_language is True
        assert config.language is None

    def test_stt_profanity_filter(self, real_credentials):
        """Test STT with profanity filter enabled."""
        stt = STT(
            api_key=real_credentials.api_key,
            folder_id=real_credentials.folder_id,
            profanity_filter=True
        )
        
        assert stt._opts.profanity_filter is True


@pytest.mark.functional
@pytest.mark.slow
class TestYandexSTTAudioProcessing:
    """Functional tests for audio processing with real audio files."""

    def test_audio_files_available(self, sample_audio_files):
        """Test that audio files are available for testing."""
        if not sample_audio_files:
            pytest.skip("No audio test files available - see tests/fixtures/README.md")
        
        print(f"Available audio files: {list(sample_audio_files.keys())}")
        
        # Check that at least one audio file exists
        assert len(sample_audio_files) > 0
        
        # Verify files actually exist
        for name, path in sample_audio_files.items():
            assert path.exists(), f"Audio file {name} does not exist at {path}"
            assert path.suffix.lower() in ['.wav', '.mp3', '.opus'], f"Unsupported audio format: {path.suffix}"

    def test_audio_file_properties(self, sample_audio_files):
        """Test audio file properties and format validation."""
        if not sample_audio_files:
            pytest.skip("No audio test files available")
        
        for name, path in sample_audio_files.items():
            # Basic file checks
            assert path.stat().st_size > 0, f"Audio file {name} is empty"
            assert path.stat().st_size < 50 * 1024 * 1024, f"Audio file {name} is too large (>50MB)"
            
            # Check file extension
            assert path.suffix.lower() in ['.wav', '.mp3', '.opus'], f"Unsupported format: {path.suffix}"

    @pytest.mark.asyncio
    async def test_stt_stream_creation_async(self, real_stt):
        """Test STT stream creation in async context."""
        # This test verifies that stream creation works in proper async context
        stream = real_stt.stream()
        assert stream is not None
        
        # Clean up
        await stream.aclose()

    def test_convert_audio_frame_function_exists(self):
        """Test that audio conversion function exists."""
        from livekit.plugins.yandex._utils import convert_audio_frame_to_pcm
        
        assert callable(convert_audio_frame_to_pcm)


@pytest.mark.functional
@pytest.mark.integration
class TestYandexSTTIntegration:
    """Integration tests that may make actual API calls."""

    def test_stt_initialization_with_env_credentials(self):
        """Test STT initialization using environment credentials."""
        # This test uses the real environment credentials
        stt = STT()
        
        assert stt._credentials.api_key is not None
        assert stt._credentials.folder_id is not None

    def test_multiple_stt_instances(self, real_credentials):
        """Test creating multiple STT instances."""
        instances = []
        
        for i in range(3):
            stt = STT(
                api_key=real_credentials.api_key,
                folder_id=real_credentials.folder_id,
                language="ru-RU" if i % 2 == 0 else "en-US"
            )
            instances.append(stt)
        
        # Verify all instances are independent
        assert len(instances) == 3
        assert instances[0]._opts.language == "ru-RU"
        assert instances[1]._opts.language == "en-US"
        assert instances[2]._opts.language == "ru-RU"

    def test_stt_configuration_validation(self, real_credentials):
        """Test STT configuration validation with real credentials."""
        # Test valid configuration
        stt = STT(
            api_key=real_credentials.api_key,
            folder_id=real_credentials.folder_id,
            language="ru-RU",
            model="general",
            sample_rate=16000
        )
        
        assert stt._opts.language == "ru-RU"
        assert stt._opts.model == "general"
        assert stt._opts.sample_rate == 16000


@pytest.mark.functional
class TestYandexSTTErrorHandling:
    """Functional tests for error handling scenarios."""

    def test_invalid_api_key(self):
        """Test STT with invalid API key."""
        with pytest.raises(ValueError, match="Yandex Cloud API key is required"):
            STT(
                api_key="",  # Empty API key
                folder_id="valid_folder_id"
            )

    def test_invalid_folder_id(self, real_credentials):
        """Test STT with invalid folder ID."""
        with pytest.raises(ValueError, match="Yandex Cloud folder_id is required"):
            STT(
                api_key=real_credentials.api_key,
                folder_id=""  # Empty folder ID
            )

    def test_missing_environment_variables(self):
        """Test behavior when environment variables are missing."""
        # Clear environment temporarily
        original_api_key = os.environ.get("YANDEX_API_KEY")
        original_folder_id = os.environ.get("YANDEX_FOLDER_ID")
        
        try:
            if "YANDEX_API_KEY" in os.environ:
                del os.environ["YANDEX_API_KEY"]
            if "YANDEX_FOLDER_ID" in os.environ:
                del os.environ["YANDEX_FOLDER_ID"]
            
            with pytest.raises(ValueError):
                STT()  # Should fail without credentials
                
        finally:
            # Restore environment
            if original_api_key:
                os.environ["YANDEX_API_KEY"] = original_api_key
            if original_folder_id:
                os.environ["YANDEX_FOLDER_ID"] = original_folder_id


@pytest.mark.functional
@pytest.mark.slow
class TestYandexSTTPerformance:
    """Performance tests for STT functionality."""

    def test_stt_initialization_performance(self, real_credentials):
        """Test STT initialization performance."""
        import time
        
        start_time = time.time()
        
        for _ in range(10):
            stt = STT(
                api_key=real_credentials.api_key,
                folder_id=real_credentials.folder_id
            )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should be able to create 10 instances in less than 1 second
        assert total_time < 1.0, f"STT initialization too slow: {total_time:.2f}s for 10 instances"

    def test_memory_usage_multiple_instances(self, real_credentials):
        """Test memory usage with multiple STT instances."""
        instances = []
        
        # Create multiple instances
        for i in range(5):
            stt = STT(
                api_key=real_credentials.api_key,
                folder_id=real_credentials.folder_id,
                language="ru-RU"
            )
            instances.append(stt)
        
        # Verify all instances are independent and functional
        assert len(instances) == 5
        for stt in instances:
            assert stt._credentials.api_key == real_credentials.api_key
            assert stt._opts.language == "ru-RU"
