# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Pytest configuration and fixtures for Yandex SpeechKit STT plugin tests.
"""

import os
import sys
from pathlib import Path
from typing import Generator

import pytest

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from livekit.plugins.yandex import STT
from livekit.plugins.yandex._utils import YandexCredentials, load_env_file


@pytest.fixture(scope="session", autouse=True)
def load_environment():
    """Load environment variables from .env file if it exists."""
    env_file = project_root / ".env"
    if env_file.exists():
        load_env_file(env_file)
        print(f"✅ Loaded environment from {env_file}")
    else:
        print(f"⚠️  No .env file found at {env_file}")


@pytest.fixture
def test_credentials() -> YandexCredentials:
    """Provide test credentials for unit tests."""
    return YandexCredentials(
        api_key="test_api_key",
        folder_id="test_folder_id",
        iam_token=None,
    )


@pytest.fixture
def real_credentials() -> YandexCredentials:
    """
    Provide real credentials from environment for functional tests.
    
    Raises:
        pytest.skip: If real credentials are not available
    """
    creds = YandexCredentials.from_env(load_dotenv=False)
    
    if not creds.folder_id:
        pytest.skip("YANDEX_FOLDER_ID not set - skipping functional test")
    
    if not creds.api_key and not creds.iam_token:
        pytest.skip("YANDEX_API_KEY or YANDEX_IAM_TOKEN not set - skipping functional test")
    
    return creds


@pytest.fixture
def test_stt(test_credentials: YandexCredentials) -> STT:
    """Create STT instance with test credentials."""
    return STT(
        api_key=test_credentials.api_key,
        folder_id=test_credentials.folder_id,
        language="ru-RU",
    )


@pytest.fixture
def real_stt(real_credentials: YandexCredentials) -> STT:
    """Create STT instance with real credentials for functional tests."""
    return STT(
        api_key=real_credentials.api_key,
        iam_token=real_credentials.iam_token,
        folder_id=real_credentials.folder_id,
        language="ru-RU",
    )


@pytest.fixture
def fixtures_dir() -> Path:
    """Path to test fixtures directory."""
    return Path(__file__).parent / "fixtures"


@pytest.fixture
def sample_audio_files(fixtures_dir: Path) -> dict[str, Path]:
    """
    Dictionary of available sample audio files.
    
    Returns:
        dict: Mapping of language codes to audio file paths
    """
    audio_files = {}
    
    # Check for Russian audio samples
    russian_files = [
        "russian_sample.wav",
        "russian_long.wav", 
        "russian_noisy.wav",
    ]
    
    for filename in russian_files:
        file_path = fixtures_dir / filename
        if file_path.exists():
            audio_files[f"ru_{filename.split('_')[1].split('.')[0]}"] = file_path
    
    # Check for English audio samples
    english_files = [
        "english_sample.wav",
        "english_long.wav",
        "english_noisy.wav", 
    ]
    
    for filename in english_files:
        file_path = fixtures_dir / filename
        if file_path.exists():
            audio_files[f"en_{filename.split('_')[1].split('.')[0]}"] = file_path
    
    # Check for mixed language samples
    mixed_files = [
        "mixed_ru_en.wav",
        "silence.wav",
    ]
    
    for filename in mixed_files:
        file_path = fixtures_dir / filename
        if file_path.exists():
            audio_files[filename.split('.')[0]] = file_path
    
    return audio_files


@pytest.fixture
def skip_if_no_audio():
    """Skip test if no audio files are available."""
    def _skip_if_no_audio(audio_files: dict):
        if not audio_files:
            pytest.skip("No audio test files available - see tests/fixtures/README.md")
    return _skip_if_no_audio


@pytest.fixture
def skip_if_no_credentials():
    """Skip test if no real credentials are available."""
    def _skip_if_no_credentials():
        if not os.environ.get("YANDEX_API_KEY") and not os.environ.get("YANDEX_IAM_TOKEN"):
            pytest.skip("No Yandex credentials available - set YANDEX_API_KEY or YANDEX_IAM_TOKEN")
        if not os.environ.get("YANDEX_FOLDER_ID"):
            pytest.skip("No Yandex folder ID available - set YANDEX_FOLDER_ID")
    return _skip_if_no_credentials


# Pytest markers for different test types
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test (no external dependencies)"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test (requires credentials)"
    )
    config.addinivalue_line(
        "markers", "functional: mark test as a functional test (requires credentials and audio)"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their names and requirements."""
    for item in items:
        # Mark integration tests
        if "integration" in item.nodeid or "real_" in str(item.function):
            item.add_marker(pytest.mark.integration)
        
        # Mark functional tests  
        if "functional" in item.nodeid or "audio" in str(item.function):
            item.add_marker(pytest.mark.functional)
        
        # Mark slow tests
        if "slow" in item.nodeid or "performance" in str(item.function):
            item.add_marker(pytest.mark.slow)
        
        # Default to unit test if no other marker
        if not any(marker.name in ["integration", "functional", "slow"] for marker in item.iter_markers()):
            item.add_marker(pytest.mark.unit)


@pytest.fixture(scope="session")
def event_loop():
    """Create an event loop for async tests."""
    import asyncio
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# Environment validation
def pytest_sessionstart(session):
    """Validate environment at session start."""
    print("\n🧪 Starting Yandex SpeechKit STT Plugin Tests")
    
    # Check if .env file exists
    env_file = project_root / ".env"
    if env_file.exists():
        print(f"✅ Found .env file: {env_file}")
    else:
        print(f"⚠️  No .env file found: {env_file}")
        print("   Create .env file for functional tests (see .env.example)")
    
    # Check for test fixtures
    fixtures_dir = project_root / "tests" / "fixtures"
    audio_files = list(fixtures_dir.glob("*.wav"))
    if audio_files:
        print(f"✅ Found {len(audio_files)} audio test files")
    else:
        print("⚠️  No audio test files found in tests/fixtures/")
        print("   Add audio files for functional tests (see tests/fixtures/README.md)")
    
    # Check for credentials (without exposing them)
    has_api_key = bool(os.environ.get("YANDEX_API_KEY"))
    has_iam_token = bool(os.environ.get("YANDEX_IAM_TOKEN"))
    has_folder_id = bool(os.environ.get("YANDEX_FOLDER_ID"))
    
    if has_api_key or has_iam_token:
        print("✅ Yandex credentials available")
    else:
        print("⚠️  No Yandex credentials found")
        print("   Set YANDEX_API_KEY or YANDEX_IAM_TOKEN for integration tests")
    
    if has_folder_id:
        print("✅ Yandex folder ID available")
    else:
        print("⚠️  No Yandex folder ID found")
        print("   Set YANDEX_FOLDER_ID for integration tests")
    
    print()


def pytest_sessionfinish(session, exitstatus):
    """Clean up after test session."""
    print(f"\n🏁 Test session finished with exit status: {exitstatus}")
