# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Pytest configuration for integration tests.

Integration tests require real Yandex Cloud credentials and test the integration
between unit components and the actual Yandex SpeechKit service.
They focus on testing the interaction between components rather than end-to-end functionality.
"""

import os
import sys
from pathlib import Path

import pytest

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from livekit.plugins.yandex import STT
from livekit.plugins.yandex._utils import YandexCredentials


@pytest.fixture
def real_credentials() -> YandexCredentials:
    """
    Provide real credentials from environment for integration tests.
    
    Raises:
        pytest.skip: If real credentials are not available
    """
    api_key = os.environ.get("YANDEX_API_KEY")
    folder_id = os.environ.get("YANDEX_FOLDER_ID")
    
    if not folder_id:
        pytest.skip("YANDEX_FOLDER_ID not set - skipping integration test")
    
    if not api_key:
        pytest.skip("YANDEX_API_KEY not set - skipping integration test")
    
    return YandexCredentials(
        api_key=api_key,
        folder_id=folder_id,
    )


@pytest.fixture
def real_stt(real_credentials: YandexCredentials) -> STT:
    """Create STT instance with real credentials for integration tests."""
    return STT(
        api_key=real_credentials.api_key,
        folder_id=real_credentials.folder_id,
        language="ru-RU",
    )


def validate_credentials() -> bool:
    """Validate that required credentials are available."""
    has_api_key = bool(os.environ.get("YANDEX_API_KEY"))
    has_folder_id = bool(os.environ.get("YANDEX_FOLDER_ID"))
    
    return has_folder_id and has_api_key


@pytest.fixture
def skip_if_no_credentials():
    """Skip test if no real credentials are available."""
    def _skip_if_no_credentials():
        if not validate_credentials():
            pytest.skip("Missing Yandex credentials - ensure YANDEX_FOLDER_ID and YANDEX_API_KEY are set")
    return _skip_if_no_credentials


# Pytest configuration for integration tests
def pytest_configure(config):
    """Configure integration test markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test (requires credentials)"
    )


def pytest_collection_modifyitems(config, items):
    """Automatically mark all tests in this directory as integration tests."""
    for item in items:
        item.add_marker(pytest.mark.integration)


@pytest.fixture(scope="session")
def event_loop():
    """Create an event loop for async tests."""
    import asyncio
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


def pytest_sessionstart(session):
    """Validate environment at integration test session start."""
    print("\nStarting Yandex SpeechKit STT Integration Tests")
    
    # Check for credentials (without exposing them)
    has_api_key = bool(os.environ.get("YANDEX_API_KEY"))
    has_folder_id = bool(os.environ.get("YANDEX_FOLDER_ID"))

    if has_api_key:
        print("Yandex credentials available")
    else:
        print("No Yandex credentials found")
        print("Set YANDEX_API_KEY for integration tests")

    if has_folder_id:
        print("Yandex folder ID available")
    else:
        print("No Yandex folder ID found")
        print("Set YANDEX_FOLDER_ID for integration tests")
    
    print()


def pytest_sessionfinish(session, exitstatus):
    """Clean up after integration test session."""
    print(f"\nIntegration test session finished with exit status: {exitstatus}")
