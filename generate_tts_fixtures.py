#!/usr/bin/env python3
"""
Generate TTS-based audio fixtures for Yandex SpeechKit STT plugin tests.

This script uses system TTS to create speech samples for testing.
Works on Windows with SAPI, and can be extended for other platforms.

Requirements:
- Windows: Built-in SAPI
- macOS: Built-in say command
- Linux: espeak or festival

Usage:
    python generate_tts_fixtures.py
"""

import platform
import subprocess
import sys
from pathlib import Path


# Test phrases for different languages
RUSSIAN_PHRASES = [
    "Привет, это тест системы распознавания речи Яндекс.",
    "Сегодня хорошая погода для прогулки в парке.",
    "Технологии искусственного интеллекта развиваются очень быстро.",
    "Москва - столица России и крупнейший город страны.",
]

ENGLISH_PHRASES = [
    "Hello, this is a test of the Yandex speech recognition system.",
    "The weather today is perfect for a walk in the park.",
    "Artificial intelligence technologies are developing very rapidly.",
    "Testing speech recognition with various sentence structures and vocabulary.",
]

MIXED_PHRASES = [
    "Hello, меня зовут Анна. I work in технологической компании.",
    "This is English, а это русский язык in the same sentence.",
]


def check_windows_tts():
    """Check if Windows TTS is available."""
    try:
        import win32com.client
        return True
    except ImportError:
        print("Windows TTS requires pywin32. Install with: pip install pywin32")
        return False


def generate_windows_tts(text: str, output_path: Path, voice_name: str = None):
    """Generate TTS audio on Windows using SAPI."""
    try:
        import win32com.client
        
        # Create SAPI voice object
        voice = win32com.client.Dispatch("SAPI.SpVoice")
        
        # Set voice if specified
        if voice_name:
            voices = voice.GetVoices()
            for i in range(voices.Count):
                if voice_name.lower() in voices.Item(i).GetDescription().lower():
                    voice.Voice = voices.Item(i)
                    break
        
        # Create file stream
        file_stream = win32com.client.Dispatch("SAPI.SpFileStream")
        file_stream.Open(str(output_path), 3)  # 3 = write mode
        
        # Set output to file
        voice.AudioOutputStream = file_stream
        
        # Speak text
        voice.Speak(text)
        
        # Close file
        file_stream.Close()
        
        print(f"  Created: {output_path.name}")
        return True
        
    except Exception as e:
        print(f"  Failed to create {output_path.name}: {e}")
        return False


def generate_macos_tts(text: str, output_path: Path, voice: str = "Alex"):
    """Generate TTS audio on macOS using say command."""
    try:
        # Create temporary AIFF file
        temp_path = output_path.with_suffix('.aiff')
        
        cmd = ["say", "-v", voice, "-o", str(temp_path), text]
        subprocess.run(cmd, check=True, capture_output=True)
        
        # Convert to WAV if ffmpeg is available
        if output_path.suffix.lower() == '.wav':
            try:
                convert_cmd = [
                    "ffmpeg", "-y", "-i", str(temp_path),
                    "-ar", "16000", "-ac", "1", "-c:a", "pcm_s16le",
                    str(output_path)
                ]
                subprocess.run(convert_cmd, check=True, capture_output=True)
                temp_path.unlink()  # Remove temp file
            except subprocess.CalledProcessError:
                # If ffmpeg fails, just rename the AIFF file
                temp_path.rename(output_path.with_suffix('.aiff'))
        
        print(f"  Created: {output_path.name}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"  Failed to create {output_path.name}: {e}")
        return False


def generate_linux_tts(text: str, output_path: Path):
    """Generate TTS audio on Linux using espeak."""
    try:
        cmd = [
            "espeak", "-w", str(output_path),
            "-s", "150",  # Speed
            "-a", "100",  # Amplitude
            text
        ]
        subprocess.run(cmd, check=True, capture_output=True)
        
        print(f"  Created: {output_path.name}")
        return True
        
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"  Failed to create {output_path.name}: {e}")
        print("  Install espeak: sudo apt-get install espeak")
        return False


def generate_tts_file(text: str, output_path: Path, language: str = "en"):
    """Generate TTS file for the current platform."""
    system = platform.system()
    
    if system == "Windows":
        # Use different voices for different languages
        voice_name = "russian" if language == "ru" else None
        return generate_windows_tts(text, output_path, voice_name)
    
    elif system == "Darwin":  # macOS
        voice = "Yuri" if language == "ru" else "Alex"
        return generate_macos_tts(text, output_path, voice)
    
    elif system == "Linux":
        return generate_linux_tts(text, output_path)
    
    else:
        print(f"Unsupported platform: {system}")
        return False


def create_tts_fixtures():
    """Create TTS-based test fixtures."""
    fixtures_dir = Path("tests/fixtures")
    fixtures_dir.mkdir(exist_ok=True)
    
    print(f"Creating TTS fixtures in: {fixtures_dir.absolute()}")
    print(f"Platform: {platform.system()}")
    print()
    
    success_count = 0
    total_count = 0
    
    # Russian samples
    print("Generating Russian samples...")
    for i, phrase in enumerate(RUSSIAN_PHRASES[:3]):  # Limit to 3 samples
        if i == 0:
            filename = "russian_sample.wav"
        elif i == 1:
            filename = "russian_long.wav"
        else:
            filename = "russian_noisy.wav"  # Will be clean, but named for consistency
        
        output_path = fixtures_dir / filename
        if generate_tts_file(phrase, output_path, "ru"):
            success_count += 1
        total_count += 1
    
    print()
    
    # English samples
    print("Generating English samples...")
    for i, phrase in enumerate(ENGLISH_PHRASES[:3]):  # Limit to 3 samples
        if i == 0:
            filename = "english_sample.wav"
        elif i == 1:
            filename = "english_long.wav"
        else:
            filename = "english_noisy.wav"  # Will be clean, but named for consistency
        
        output_path = fixtures_dir / filename
        if generate_tts_file(phrase, output_path, "en"):
            success_count += 1
        total_count += 1
    
    print()
    
    # Mixed language sample
    print("Generating mixed language sample...")
    mixed_text = " ".join(MIXED_PHRASES)
    output_path = fixtures_dir / "mixed_ru_en.wav"
    if generate_tts_file(mixed_text, output_path, "en"):
        success_count += 1
    total_count += 1
    
    print()
    print(f"TTS fixtures created: {success_count}/{total_count} successful")
    
    if success_count > 0:
        print()
        print("TTS fixtures created successfully!")
        print()
        print("Note: These are synthetic speech samples.")
        print("For better testing, consider using real human speech recordings.")
    
    return success_count > 0


def list_available_voices():
    """List available TTS voices on the system."""
    system = platform.system()
    
    print(f"Available TTS voices on {system}:")
    print()
    
    if system == "Windows":
        try:
            import win32com.client
            voice = win32com.client.Dispatch("SAPI.SpVoice")
            voices = voice.GetVoices()
            
            for i in range(voices.Count):
                voice_info = voices.Item(i)
                name = voice_info.GetDescription()
                print(f"  {i}: {name}")
                
        except ImportError:
            print("  Windows TTS requires pywin32. Install with: pip install pywin32")
        except Exception as e:
            print(f"  Error listing voices: {e}")
    
    elif system == "Darwin":  # macOS
        try:
            result = subprocess.run(["say", "-v", "?"], capture_output=True, text=True)
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    print(f"  {line}")
        except Exception as e:
            print(f"  Error listing voices: {e}")
    
    elif system == "Linux":
        print("  espeak voices (install espeak for TTS support)")
        try:
            result = subprocess.run(["espeak", "--voices"], capture_output=True, text=True)
            for line in result.stdout.strip().split('\n')[1:]:  # Skip header
                if line.strip():
                    print(f"  {line}")
        except Exception:
            print("  espeak not installed")
    
    else:
        print(f"  Voice listing not supported on {system}")


def main():
    """Main function."""
    print("Yandex SpeechKit STT Plugin - TTS Fixture Generator")
    print("=" * 55)
    print()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--list-voices":
        list_available_voices()
        return
    
    # Check platform-specific requirements
    system = platform.system()
    if system == "Windows" and not check_windows_tts():
        sys.exit(1)
    
    try:
        if create_tts_fixtures():
            print()
            print("Run 'python generate_tts_fixtures.py --list-voices' to see available voices.")
        else:
            print("Failed to create TTS fixtures. Try the basic fixture generator:")
            print("  python generate_fixtures.py")
            
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nError: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
