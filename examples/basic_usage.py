#!/usr/bin/env python3
"""
Basic usage example for Yandex SpeechKit STT plugin.

This example demonstrates how to use the Yandex SpeechKit STT plugin
with LiveKit Agents for real-time speech recognition.

Prerequisites:
1. Clone this repository: git clone https://github.com/your-username/livekit-plugins-yandex.git
2. Install in development mode: pip install -e .
3. Set up .env file with your Yandex Cloud credentials
4. Run this example: python examples/basic_usage.py
"""

import asyncio
import os
from livekit.agents import AgentSession, WorkerOptions, cli
from livekit.plugins import yandex


async def entrypoint(ctx: AgentSession):
    """Main agent entry point."""

    # Create Yandex STT instance
    # Credentials will be automatically loaded from .env file or environment variables
    stt = yandex.STT(
        language="ru-RU",  # Russian language
        interim_results=True,
        profanity_filter=False,
    )
    
    print("🎤 Yandex SpeechKit STT Agent started")
    print("📝 Listening for speech...")
    
    # Create streaming session
    stream = stt.stream()
    
    try:
        # Process speech events
        async for event in stream:
            if event.type == "interim_transcript":
                # Interim results (partial transcription)
                text = event.alternatives[0].text if event.alternatives else ""
                if text.strip():
                    print(f"🔄 Interim: {text}")
                    
            elif event.type == "final_transcript":
                # Final results (complete transcription)
                text = event.alternatives[0].text if event.alternatives else ""
                if text.strip():
                    print(f"✅ Final: {text}")
                    
            elif event.type == "start_of_speech":
                print("🗣️  Speech started")
                
            elif event.type == "end_of_speech":
                print("🔇 Speech ended")
                
    except KeyboardInterrupt:
        print("\n👋 Stopping agent...")
    finally:
        await stream.aclose()


def main():
    """Main function to run the agent."""

    # Check for .env file or environment variables
    from livekit.plugins.yandex._utils import YandexCredentials

    creds = YandexCredentials.from_env()

    if not creds.api_key and not creds.iam_token:
        print("❌ Error: YANDEX_API_KEY is required")
        print("   Create a .env file with: YANDEX_API_KEY=your_api_key")
        print("   Or set environment variable: export YANDEX_API_KEY='your_api_key'")
        return

    if not creds.folder_id:
        print("❌ Error: YANDEX_FOLDER_ID is required")
        print("   Add to .env file: YANDEX_FOLDER_ID=your_folder_id")
        print("   Or set environment variable: export YANDEX_FOLDER_ID='your_folder_id'")
        return
    
    print("🚀 Starting Yandex SpeechKit STT example...")
    print(f"📁 Folder ID: {creds.folder_id}")
    print(f"🔑 Using API Key: {'✅' if creds.api_key else '❌'}")
    print(f"🎫 Using IAM Token: {'✅' if creds.iam_token else '❌'}")
    
    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            # Add any additional worker options here
        )
    )


if __name__ == "__main__":
    main()
