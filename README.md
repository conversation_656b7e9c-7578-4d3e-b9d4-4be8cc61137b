
# Yandex SpeechKit STT Plugin for LiveKit Agents

This plugin provides Yandex SpeechKit Speech-to-Text (STT) integration for LiveKit Agents, enabling real-time Russian and English speech recognition.

## Features

- **Real-time streaming STT** using Yandex SpeechKit v3 API
- **Multi-language support** with primary focus on Russian and English
- **Automatic language detection** capabilities
- **Interim results** for responsive user experience
- **Profanity filtering** and text normalization options
- **Seamless LiveKit integration** following established plugin patterns

## Installation

### Prerequisites

**Windows users**: Protocol Buffers compiler is required for gRPC stub generation:
```bash
choco install protoc
```

**macOS users**:
```bash
brew install protobuf
```

**Ubuntu/Debian users**:
```bash
apt-get install protobuf-compiler
```

### Install the Plugin

```bash
pip install livekit-plugins-yandex
```

## Configuration

### Environment Setup

1. **Copy the environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file** with your Yandex Cloud credentials:
   ```bash
   # Required
   YANDEX_API_KEY=your_service_account_key
   YANDEX_FOLDER_ID=your_folder_id

   # Optional (alternative to API key)
   YANDEX_IAM_TOKEN=your_iam_token

   # Optional (custom endpoint)
   YANDEX_STT_ENDPOINT=stt.api.cloud.yandex.net:443
   ```

3. **Alternative: Environment Variables**

   You can also set environment variables directly:
   ```bash
   # Required
   export YANDEX_API_KEY="your_service_account_key"
   export YANDEX_FOLDER_ID="your_folder_id"

   # Optional (alternative to API key)
   export YANDEX_IAM_TOKEN="your_iam_token"

   # Optional (custom endpoint)
   export YANDEX_STT_ENDPOINT="stt.api.cloud.yandex.net:443"
   ```

### Yandex Cloud Setup

1. **Create a Yandex Cloud account** at [cloud.yandex.com](https://cloud.yandex.com)
2. **Create a folder** in your cloud
3. **Create a service account** with `speechkit.stt` permissions
4. **Generate an API key** for the service account

## Usage

### Basic Usage

```python
from livekit.agents import AgentSession
from livekit.plugins import yandex

# Create STT instance
stt = yandex.STT(
    language="ru-RU",  # Russian
    api_key="your_api_key",
    folder_id="your_folder_id"
)

# Use in LiveKit Agent
agent = AgentSession(
    stt=stt,
    # ... other configuration
)
```

### Advanced Configuration

```python
from livekit.plugins import yandex

# With language detection
stt = yandex.STT(
    detect_language=True,
    interim_results=True,
    profanity_filter=True,
    model="general"
)

# English-only recognition
stt = yandex.STT(
    language="en-US",
    model="general",
    sample_rate=16000
)
```

### Streaming Recognition

```python
import asyncio
from livekit.plugins import yandex

async def transcribe_stream():
    stt = yandex.STT(language="ru-RU")

    # Create streaming session
    stream = stt.stream()

    # Process audio frames
    async for event in stream:
        if event.type == "final_transcript":
            print(f"Final: {event.alternatives[0].text}")
        elif event.type == "interim_transcript":
            print(f"Interim: {event.alternatives[0].text}")
```

## Supported Languages

Primary languages with full support:
- **Russian** (`ru-RU`)
- **English** (`en-US`)

Additional supported languages:
- Turkish (`tr-TR`)
- Kazakh (`kk-KK`)
- Uzbek (`uz-UZ`)
- Armenian (`hy-AM`)
- Hebrew (`he-IL`)
- Arabic (`ar`)
- And many more...

## Configuration Options

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `model` | `str` | `"general"` | Recognition model |
| `language` | `str` | `"ru-RU"` | Language code |
| `detect_language` | `bool` | `False` | Auto language detection |
| `interim_results` | `bool` | `True` | Enable interim results |
| `profanity_filter` | `bool` | `False` | Filter profanity |
| `sample_rate` | `int` | `16000` | Audio sample rate |
| `api_key` | `str` | `None` | Yandex Cloud API key |
| `iam_token` | `str` | `None` | Yandex Cloud IAM token |
| `folder_id` | `str` | `None` | Yandex Cloud folder ID |

## Error Handling

The plugin includes comprehensive error handling for:
- **Authentication failures** (invalid API keys/tokens)
- **Network connectivity issues** (timeouts, connection drops)
- **Rate limiting** (quota exceeded)
- **Audio format validation** (unsupported formats)
- **gRPC communication errors** (service unavailable)

## Development Status

🚧 **This plugin is currently in development.**

Current status:
- ✅ Project structure and configuration
- ✅ Authentication system
- ✅ Basic STT interface implementation
- 🚧 gRPC streaming implementation (in progress)
- 🚧 Full proto stub generation
- 🚧 Comprehensive testing

## Contributing

Contributions are welcome! Please see the [LiveKit Agents repository](https://github.com/livekit/agents) for contribution guidelines.

## License

This plugin is licensed under the Apache License 2.0. See [LICENSE](LICENSE) for details.

## Support

- **Documentation**: [LiveKit Docs](https://docs.livekit.io)
- **Community**: [LiveKit Discord](https://livekit.io/discord)
- **Issues**: [GitHub Issues](https://github.com/livekit/agents/issues)
```
