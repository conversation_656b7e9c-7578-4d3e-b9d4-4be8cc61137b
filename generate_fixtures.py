#!/usr/bin/env python3
"""
Generate basic audio fixtures for Yandex SpeechKit STT plugin tests.

This script creates simple audio fixtures that can be used for testing.
For production testing, replace these with real speech samples.

Requirements:
- FFmpeg installed and in PATH
- Python 3.9+

Usage:
    python generate_fixtures.py
"""

import subprocess
import sys
from pathlib import Path


def check_ffmpeg():
    """Check if FFmpeg is available."""
    try:
        result = subprocess.run(
            ["ffmpeg", "-version"], 
            capture_output=True, 
            text=True, 
            check=True
        )
        print("FFmpeg found:", result.stdout.split('\n')[0])
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Error: FFmpeg not found. Please install FFmpeg:")
        print("  Windows: choco install ffmpeg")
        print("  macOS: brew install ffmpeg")
        print("  Ubuntu: apt-get install ffmpeg")
        return False


def generate_silence(output_path: Path, duration: int = 5):
    """Generate a silence audio file."""
    print(f"Generating silence: {output_path}")
    
    cmd = [
        "ffmpeg", "-y",  # Overwrite existing files
        "-f", "lavfi",
        "-i", f"anullsrc=r=16000:cl=mono",
        "-t", str(duration),
        "-c:a", "pcm_s16le",
        str(output_path)
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        print(f"  Created: {output_path} ({duration}s)")
    except subprocess.CalledProcessError as e:
        print(f"  Failed to create {output_path}: {e}")


def generate_tone(output_path: Path, frequency: int = 440, duration: int = 5):
    """Generate a tone audio file."""
    print(f"Generating tone: {output_path}")
    
    cmd = [
        "ffmpeg", "-y",
        "-f", "lavfi",
        "-i", f"sine=frequency={frequency}:sample_rate=16000:duration={duration}",
        "-c:a", "pcm_s16le",
        str(output_path)
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        print(f"  Created: {output_path} ({frequency}Hz, {duration}s)")
    except subprocess.CalledProcessError as e:
        print(f"  Failed to create {output_path}: {e}")


def generate_noise(output_path: Path, duration: int = 5):
    """Generate a white noise audio file."""
    print(f"Generating noise: {output_path}")
    
    cmd = [
        "ffmpeg", "-y",
        "-f", "lavfi",
        "-i", f"anoisesrc=d={duration}:c=pink:r=16000:a=0.1",
        "-c:a", "pcm_s16le",
        str(output_path)
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        print(f"  Created: {output_path} (noise, {duration}s)")
    except subprocess.CalledProcessError as e:
        print(f"  Failed to create {output_path}: {e}")


def generate_mixed_tone(output_path: Path, duration: int = 10):
    """Generate a mixed frequency tone to simulate speech patterns."""
    print(f"Generating mixed tone: {output_path}")
    
    cmd = [
        "ffmpeg", "-y",
        "-f", "lavfi",
        "-i", f"sine=f=300:d={duration/3}:r=16000",
        "-f", "lavfi", 
        "-i", f"sine=f=500:d={duration/3}:r=16000",
        "-f", "lavfi",
        "-i", f"sine=f=400:d={duration/3}:r=16000",
        "-filter_complex", "[0:a][1:a][2:a]concat=n=3:v=0:a=1",
        "-c:a", "pcm_s16le",
        str(output_path)
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        print(f"  Created: {output_path} (mixed tones, {duration}s)")
    except subprocess.CalledProcessError as e:
        print(f"  Failed to create {output_path}: {e}")


def create_fixtures():
    """Create all test fixtures."""
    fixtures_dir = Path("tests/fixtures")
    fixtures_dir.mkdir(exist_ok=True)
    
    print(f"Creating fixtures in: {fixtures_dir.absolute()}")
    print()
    
    # Basic fixtures
    generate_silence(fixtures_dir / "silence.wav", 5)
    
    # Russian samples (using tones as placeholders)
    generate_tone(fixtures_dir / "russian_sample.wav", 300, 8)
    generate_mixed_tone(fixtures_dir / "russian_long.wav", 30)
    generate_noise(fixtures_dir / "russian_noisy.wav", 10)
    
    # English samples (using different tones)
    generate_tone(fixtures_dir / "english_sample.wav", 400, 8)
    generate_mixed_tone(fixtures_dir / "english_long.wav", 25)
    generate_noise(fixtures_dir / "english_noisy.wav", 10)
    
    # Mixed language sample
    generate_mixed_tone(fixtures_dir / "mixed_ru_en.wav", 15)
    
    print()
    print("Basic fixtures created successfully!")
    print()
    print("IMPORTANT: These are placeholder audio files (tones and noise).")
    print("For real testing, replace them with actual speech samples:")
    print("  - Record Russian and English speech")
    print("  - Use text-to-speech tools")
    print("  - Convert existing audio files")
    print()
    print("See tests/fixtures/README.md for detailed instructions.")


def validate_fixtures():
    """Validate created fixtures."""
    fixtures_dir = Path("tests/fixtures")
    
    if not fixtures_dir.exists():
        print("No fixtures directory found.")
        return False
    
    audio_files = list(fixtures_dir.glob("*.wav"))
    
    if not audio_files:
        print("No audio fixtures found.")
        return False
    
    print(f"Found {len(audio_files)} audio fixtures:")
    
    total_size = 0
    for audio_file in sorted(audio_files):
        size = audio_file.stat().st_size
        total_size += size
        print(f"  {audio_file.name}: {size:,} bytes")
    
    print(f"Total size: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")
    
    if total_size > 50 * 1024 * 1024:  # 50MB
        print("Warning: Total fixture size is large (>50MB)")
    
    return True


def main():
    """Main function."""
    print("Yandex SpeechKit STT Plugin - Fixture Generator")
    print("=" * 50)
    print()
    
    if not check_ffmpeg():
        sys.exit(1)
    
    print()
    
    try:
        create_fixtures()
        print()
        validate_fixtures()
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nError: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
