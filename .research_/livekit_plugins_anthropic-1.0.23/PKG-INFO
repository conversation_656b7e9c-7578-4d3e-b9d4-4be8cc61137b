Metadata-Version: 2.4
Name: livekit-plugins-anthropic
Version: 1.0.23
Summary: Agent Framework plugin for services from Anthropic
Project-URL: Documentation, https://docs.livekit.io
Project-URL: Website, https://livekit.io/
Project-URL: Source, https://github.com/livekit/agents
Author-email: LiveKit <<EMAIL>>
License-Expression: Apache-2.0
Keywords: audio,livekit,realtime,video,webrtc
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Topic :: Multimedia :: Sound/Audio
Classifier: Topic :: Multimedia :: Video
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.9.0
Requires-Dist: anthropic>=0.41
Requires-Dist: livekit-agents>=1.0.23
Description-Content-Type: text/markdown

# Anthropic plugin for LiveKit Agents

Support for the Claude family of LLMs from Anthropic.

See [https://docs.livekit.io/agents/integrations/llm/anthropic/](https://docs.livekit.io/agents/integrations/llm/anthropic/) for more information.

## Installation

```bash
pip install livekit-plugins-anthropic
```

## Pre-requisites

You'll need an API key from Anthropic. It can be set as an environment variable: `ANTHROPIC_API_KEY`
