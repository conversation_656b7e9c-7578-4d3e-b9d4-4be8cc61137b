[build-system]
requires = ["setuptools>=61", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "livekit-plugins-yandex"
version = "0.1.0"
description = "Yandex SpeechKit plugin for LiveKit Agents"
readme = "README.md"
license = {text = "Apache-2.0"}
authors = [
    {name = "LiveKit", email = "<EMAIL>"},
]
keywords = ["livekit", "agents", "stt", "yandex", "speechkit"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.9"
dependencies = [
    "livekit-agents>=0.8.0",
    "grpcio>=1.50.0",
    "grpcio-tools>=1.50.0",
    "protobuf>=4.0.0",
    "aiohttp>=3.8.0",
    "numpy>=1.20.0",
    "pydantic>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "ruff>=0.1.0",
]

[project.urls]
Homepage = "https://livekit.io"
Documentation = "https://docs.livekit.io"
Repository = "https://github.com/livekit/agents"
"Bug Tracker" = "https://github.com/livekit/agents/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["livekit*"]

[tool.setuptools.package-data]
"livekit.plugins.yandex" = ["py.typed"]

[tool.black]
line-length = 100
target-version = ["py39"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.ruff]
line-length = 100
target-version = "py39"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501", # line too long, handled by black
    "B008", # do not perform function calls in argument defaults
    "C901", # too complex
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
addopts = "-v --tb=short"
