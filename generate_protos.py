#!/usr/bin/env python3
"""
Script to generate gRPC Python stubs from Yandex Cloud API proto files.
"""

import os
import subprocess
import sys
from pathlib import Path


def generate_grpc_stubs():
    """Generate gRPC stubs from proto files."""
    
    # Paths
    proto_dir = Path(".research/cloudapi")
    output_dir = Path("livekit/plugins/yandex/grpc_stubs")
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Proto files we need
    proto_files = [
        "yandex/cloud/ai/stt/v3/stt.proto",
        "yandex/cloud/ai/stt/v3/stt_service.proto",
        "yandex/cloud/validation.proto",
        "yandex/cloud/api/operation.proto",
        "yandex/cloud/operation/operation.proto",
    ]

    # Include Google API proto files from third_party
    google_proto_files = [
        "third_party/googleapis/google/api/annotations.proto",
        "third_party/googleapis/google/api/http.proto",
        "third_party/googleapis/google/rpc/status.proto",
        "third_party/googleapis/google/rpc/code.proto",
        "third_party/googleapis/google/rpc/error_details.proto",
    ]
    
    # Check if protoc is available
    try:
        subprocess.run(["protoc", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Error: protoc not found. Please install Protocol Buffers compiler.")
        print("On Windows: choco install protoc")
        print("On macOS: brew install protobuf")
        print("On Ubuntu: apt-get install protobuf-compiler")
        sys.exit(1)
    
    # Generate Python stubs
    cmd = [
        "python", "-m", "grpc_tools.protoc",
        f"--proto_path={proto_dir}",
        f"--python_out={output_dir}",
        f"--grpc_python_out={output_dir}",
    ]
    
    # Add proto files that exist
    existing_files = []
    all_proto_files = proto_files + google_proto_files

    for proto_file in all_proto_files:
        full_path = proto_dir / proto_file
        if full_path.exists():
            existing_files.append(str(proto_file))
        else:
            print(f"Warning: Proto file not found: {full_path}")

    if not existing_files:
        print("Error: No proto files found!")
        sys.exit(1)

    cmd.extend(existing_files)
    
    print(f"Generating gRPC stubs...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ gRPC stubs generated successfully!")
        
        # Create __init__.py files
        init_dirs = []
        for root, dirs, files in os.walk(output_dir):
            if any(f.endswith('.py') for f in files):
                init_dirs.append(root)
        
        for dir_path in init_dirs:
            init_file = Path(dir_path) / "__init__.py"
            if not init_file.exists():
                init_file.touch()
                print(f"Created {init_file}")
                
    except subprocess.CalledProcessError as e:
        print(f"Error generating stubs: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        sys.exit(1)


if __name__ == "__main__":
    generate_grpc_stubs()
