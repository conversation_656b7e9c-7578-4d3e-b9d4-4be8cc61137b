# Yandex SpeechKit STT Plugin Implementation Plan

## Overview
This document outlines the detailed implementation plan for creating a Yandex SpeechKit Speech-to-Text (STT) plugin that integrates seamlessly with the LiveKit Python Agents framework.

## Phase 1: Project Structure and Dependencies

### 1.1 Directory Structure
```
livekit-plugins-yandex/
├── livekit/
│   └── plugins/
│       └── yandex/
│           ├── __init__.py          # Plugin exports and version
│           ├── stt.py               # Main STT implementation
│           ├── models.py            # Data models and enums
│           ├── log.py               # Logging configuration
│           ├── _utils.py            # Utility functions
│           ├── version.py           # Version information
│           └── py.typed             # Type hints marker
├── pyproject.toml                   # Project configuration
├── README.md                        # Documentation
├── tests/
│   ├── __init__.py
│   ├── test_stt.py                  # Main test suite
│   └── fixtures/
│       ├── russian_sample.wav       # Test audio files
│       └── english_sample.wav
└── examples/
    └── basic_usage.py               # Usage examples
```

### 1.2 Required Dependencies
- `grpcio>=1.50.0` - gRPC communication
- `grpcio-tools>=1.50.0` - gRPC code generation
- `protobuf>=4.0.0` - Protocol buffers
- `aiohttp>=3.8.0` - HTTP session management
- `numpy>=1.20.0` - Audio processing
- `livekit-agents` - Core LiveKit agents framework

## Phase 2: Core Implementation Components

### 2.1 Authentication System
- **Yandex Cloud IAM Token**: Primary authentication method
- **API Key**: Alternative authentication via service account key
- **Environment Variables**: `YANDEX_API_KEY`, `YANDEX_IAM_TOKEN`, `YANDEX_FOLDER_ID`
- **Token Refresh**: Automatic IAM token renewal mechanism

### 2.2 Audio Processing
- **Format Conversion**: LiveKit AudioFrame → LINEAR16_PCM
- **Sample Rate**: Support 16kHz (default), 8kHz, 48kHz
- **Channel Count**: Mono audio (single channel)
- **Chunk Management**: Real-time streaming with proper buffering

### 2.3 gRPC Streaming Implementation
- **Service**: `speechkit.stt.v3.Recognizer.RecognizeStreaming`
- **Bidirectional Streaming**: Handle `StreamingRequest` ↔ `StreamingResponse`
- **Connection Management**: Auto-reconnection with exponential backoff
- **Session Lifecycle**: Proper initialization and cleanup

### 2.4 Language Support
- **Primary Languages**: Russian (`ru-RU`), English (`en-US`)
- **Language Detection**: Automatic language identification
- **Multi-language**: Support for mixed-language audio

## Phase 3: Class Architecture

### 3.1 Main STT Class
```python
class STT(stt.STT):
    def __init__(
        self,
        *,
        model: str = "general",
        language: str = "ru-RU",
        detect_language: bool = False,
        interim_results: bool = True,
        profanity_filter: bool = False,
        sample_rate: int = 16000,
        api_key: str | None = None,
        iam_token: str | None = None,
        folder_id: str | None = None,
        grpc_endpoint: str = "stt.api.cloud.yandex.net:443",
    )
```

### 3.2 Streaming Class
```python
class SpeechStream(stt.SpeechStream):
    def __init__(
        self,
        *,
        stt: STT,
        opts: STTOptions,
        conn_options: APIConnectOptions,
        credentials: YandexCredentials,
        grpc_channel: grpc.Channel,
    )
```

### 3.3 Configuration Classes
```python
@dataclass
class STTOptions:
    model: str
    language: str | None
    detect_language: bool
    interim_results: bool
    profanity_filter: bool
    sample_rate: int
    audio_encoding: str
    folder_id: str

@dataclass
class YandexCredentials:
    api_key: str | None
    iam_token: str | None
    folder_id: str
```

## Phase 4: Integration Points

### 4.1 LiveKit STT Interface Compliance
- **Batch Recognition**: Implement `_recognize_impl()` method
- **Streaming**: Implement `stream()` method returning `SpeechStream`
- **Events**: Emit proper `SpeechEvent` objects with timing
- **Capabilities**: Define `STTCapabilities(streaming=True, interim_results=True)`

### 4.2 Error Handling
- **gRPC Errors**: Connection failures, service unavailable
- **Authentication**: Invalid credentials, expired tokens
- **Rate Limiting**: Quota exceeded, throttling
- **Audio Validation**: Unsupported formats, invalid sample rates
- **Network Issues**: Timeouts, connection drops

### 4.3 Event Processing
- **Partial Results**: Convert to `SpeechEventType.INTERIM_TRANSCRIPT`
- **Final Results**: Convert to `SpeechEventType.FINAL_TRANSCRIPT`
- **EOU Events**: Handle End of Utterance detection
- **Timing**: Accurate word-level timestamps

## Phase 5: Testing Strategy

### 5.1 Unit Tests
- Authentication mechanism validation
- Audio format conversion accuracy
- gRPC message serialization/deserialization
- Error handling scenarios
- Configuration validation

### 5.2 Integration Tests
- Real Yandex Cloud API connectivity
- Russian and English audio transcription
- Streaming performance and latency
- Reconnection and recovery mechanisms
- Multi-language detection

### 5.3 Test Fixtures
- Sample audio files (Russian/English, 16kHz WAV)
- Mock gRPC responses for offline testing
- Test credentials and configuration
- Performance benchmarks

## Phase 6: Implementation Order

1. **Project Setup** (Day 1)
   - Create directory structure
   - Setup pyproject.toml with dependencies
   - Generate gRPC stubs from Yandex proto files

2. **Authentication** (Day 1-2)
   - Implement credential management
   - Add IAM token handling
   - Environment variable configuration

3. **Core STT Class** (Day 2-3)
   - Basic STT class structure
   - Configuration management
   - gRPC channel setup

4. **Audio Processing** (Day 3-4)
   - AudioFrame conversion utilities
   - Format validation
   - Sample rate handling

5. **Streaming Implementation** (Day 4-6)
   - SpeechStream class
   - Bidirectional gRPC streaming
   - Event processing and conversion

6. **Error Handling** (Day 6-7)
   - Comprehensive error handling
   - Retry mechanisms
   - Logging integration

7. **Testing** (Day 7-9)
   - Unit test suite
   - Integration tests with real API
   - Performance testing

8. **Documentation** (Day 9-10)
   - README with setup instructions
   - API documentation
   - Usage examples

## Success Criteria

### Functional Requirements
✅ Real-time Russian and English speech transcription  
✅ Seamless LiveKit Agents integration  
✅ Support for streaming and batch recognition  
✅ Proper Yandex Cloud authentication  

### Technical Requirements
✅ Follows LiveKit STT plugin patterns  
✅ Comprehensive error handling and logging  
✅ Performance comparable to existing plugins  
✅ Full test coverage with real audio samples  

### Documentation Requirements
✅ Clear installation and setup instructions  
✅ API reference documentation  
✅ Usage examples and best practices  
✅ Troubleshooting guide  

## Environment Variables

```bash
# Required
YANDEX_API_KEY=your_service_account_key
YANDEX_FOLDER_ID=your_folder_id

# Optional
YANDEX_IAM_TOKEN=your_iam_token
YANDEX_STT_ENDPOINT=stt.api.cloud.yandex.net:443
```

## Usage Example

```python
from livekit.plugins import yandex

# Basic usage
stt = yandex.STT(
    language="ru-RU",
    api_key="your_api_key",
    folder_id="your_folder_id"
)

# With language detection
stt = yandex.STT(
    detect_language=True,
    interim_results=True
)

# In LiveKit Agent
agent = AgentSession(
    stt=stt,
    # ... other configuration
)
```
